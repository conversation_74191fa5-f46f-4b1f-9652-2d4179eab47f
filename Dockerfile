FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:node-18.20.3-alpine AS build
#FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:node-16.3.0-alpine-ssh-s3-latest
RUN >/root/.ssh/known_hosts
WORKDIR /root/.ssh
RUN echo "StrictHostKeyChecking no" > config
WORKDIR /var/www/app
RUN apk update
#RUN apk add --no-cache python3 py3-pip make gcc && pip3 install --upgrade pip && pip3 install --no-cache-dir awscli && rm -rf /var/cache/apk/*
RUN apk add --no-cache python3 py3-pip && rm -rf /var/cache/apk/*
RUN apk add --no-cache make gcc aws-cli
RUN apk add --no-cache git openssh
ENV JAVA_HOME /usr/lib/jvm/java-1.8-openjdk
ENV PATH $PATH:/usr/lib/jvm/java-1.8-openjdk/jre/bin:/usr/lib/jvm/java-1.8-openjdk/bin
RUN echo $PATH
COPY . .
RUN npm cache clean --force
RUN npm install --force
# RUN npm run sonar-scanner -Dsonar.projectKey=stocks-mini -Dsonar.sources=. -Dsonar.host.url=https://sonarqube-dashboard.paytmmoney.com -Dsonar.login=****************************************
# RUN npm run sonar-scanner
RUN npm run build
ARG bucket_name
RUN aws s3 cp build  s3://$bucket_name/ --recursive
