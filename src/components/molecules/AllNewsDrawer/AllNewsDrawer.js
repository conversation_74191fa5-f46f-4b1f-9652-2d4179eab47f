// eslint-disable-next-line import/no-extraneous-dependencies
import cx from 'classnames';
import { Card } from '@paytm-h5-common/paytm_common_ui';
import BackArrowIconDark from '@assets/icons/back_arrow_header_darkmode.svg';
import BackArrowIconLight from '@assets/icons/back_arrow_header_lightmode.svg';
import FallbackCompanyIcon from '@assets/icons/company_fallback_icon.svg';
import noNewsIcon from '@assets/icons/no_news.svg';
import { useEffect, useState, useRef, useCallback } from 'react';
import styles from './AllNewsDrawer.scss';
import StockChange from '../../organisms/StockChange/StockChange';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import Shimmer from '../../atoms/Shimmer';
import { getGenericAppHeaders, makeApiGetCall } from '../../../utils/apiUtil';
import { NEWS_WIDGET_API } from '../../../config/urlConfig';
import { getFormattedNewsItem } from '../../../pages/NewsFeedWidgetPage/newsUtils';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { exitApp, openDeepLinkPaytmMoney } from '../../../utils/bridgeUtils';
import { newsWidgetListDeeplink } from '../../../utils/constants';
import NewsFeedDrawerWrapper from '../../../pages/NewsFeedList';
// import { callOrderPadLiteDrawer } from '../../../utils/navigationUtil';
import { NEWS_CATEGORY, WIDGET_EVENTS } from './enums';
import { useAnalyticsEventForWidget } from '../../../hooks/analyticsHooks';
import { isDarkMode } from '../../../utils/commonUtil';

const ALL_NEWS_ITEMS = JSON.parse(localStorage.getItem('ALL_NEWS'));
const noop = () => {};

// Debounce utility function
const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

const AllNewsDrawer = ({
  newsItemsList = ALL_NEWS_ITEMS?.newsItems || [],
  // onClose = noop,
  onCardClick = noop,
  isOpen = true,
  setIsAllNewsDrawerOpen,
  companyPageNavigation = noop,
  navigateTo,
  history,
}) => {
  const [activeTab, setActiveTab] = useState(NEWS_CATEGORY.HOT);
  const [isLoading, setIsloading] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [page, setPage] = useState(0);
  const [localNewsItems, setLocalNewsItems] = useState(newsItemsList);
  const [selectedNews, setSelectedNews] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const newsListRef = useRef(null);
  const loadMoreRef = useRef(null);

  const { sendAnalyticsEventWidget } = useAnalyticsEventForWidget();

  const handleBackClick = () => {
    if (isPaytmMoney()) {
      exitApp();
    } else {
      setIsAllNewsDrawerOpen(false);
    }
  };

  // Create a unique key for each news item
  const getUniqueKey = (item) => {
    const timestamp = item.timestamp || '';
    const id = item?.id || '';
    const isin = item?.isin || '';
    return `${id}-${isin}-${timestamp}`;
  };

  useEffect(() => {
    setIsloading(false);
    setIsFetchingMore(false);
    setLocalNewsItems(newsItemsList);
  }, [newsItemsList]);

  const fetchMoreNews = useCallback(
    async (section, from) => {
      const apiUrl = NEWS_WIDGET_API.CONSICE_API_URL;
      const queryParams = {
        section,
        from,
      };
      const headers = getGenericAppHeaders();
      try {
        const response = await makeApiGetCall({
          url: apiUrl,
          headers,
          queryParams,
        });
        if (response?.data?.data) {
          const newsResp = response?.data?.data;
          const formattedItems = newsResp?.results.map((item) =>
            getFormattedNewsItem(item),
          );
          const updatedNewsList =
            from > 0
              ? [...localNewsItems, ...formattedItems]
              : [...formattedItems];
          setLocalNewsItems(updatedNewsList);
          setIsloading(false);
          localStorage.setItem(
            'ALL_NEWS',
            JSON.stringify({
              newsItems: updatedNewsList,
              section,
              from,
            }),
          );
        }
      } catch (error) {
        console.error('Error fetching news detail:', error);
      } finally {
        setIsFetchingMore(false);
      }
    },
    [localNewsItems],
  );

  const handleIntersection = useCallback(
    (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && !isFetchingMore && !isLoading) {
        setIsFetchingMore(true);
        const section = activeTab?.split(/\s/)[0]?.toUpperCase();
        fetchMoreNews(section, page + 1);
        setPage((prevPage) => prevPage + 1);
      }
    },
    [isFetchingMore, isLoading, activeTab, fetchMoreNews, page],
  );

  // Debounced analytics event for scrolling
  const debouncedScrollAnalytics = useCallback(
    debounce(() => {
      sendAnalyticsEventWidget(WIDGET_EVENTS.VIEW_ALL_SCROLL);
    }, 300), // 300ms debounce delay
    [sendAnalyticsEventWidget],
  );

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      root: newsListRef.current,
      rootMargin: '100px',
      threshold: 0.1,
    });

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    // Add scroll event listener with debounce
    const newsListElement = newsListRef.current;
    if (newsListElement) {
      newsListElement.addEventListener('scroll', debouncedScrollAnalytics);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
      // Clean up scroll event listener
      if (newsListElement) {
        newsListElement.removeEventListener('scroll', debouncedScrollAnalytics);
      }
    };
  }, [handleIntersection, debouncedScrollAnalytics]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setIsloading(true);
    setPage(0);
    const section = tab?.split(/\s/)[0]?.toUpperCase();
    sendAnalyticsEventWidget({
      ...WIDGET_EVENTS.VIEW_ALL_TAB,
      label: tab,
    });
    fetchMoreNews(section, 0);
  };

  const handleNewsCardClick = async (item, e) => {
    e.preventDefault();
    e.stopPropagation();
    // Call the onCardClick prop first (for event trigger only)
    onCardClick(item, e, true);

    const apiUrl = NEWS_WIDGET_API.DETAILS_API_URL;
    const queryParams = {
      isin: item.isin,
      sno: item.sno,
    };

    const headers = getGenericAppHeaders();
    try {
      const response = await makeApiGetCall({
        url: apiUrl,
        headers,
        queryParams,
      });

      if (response?.data?.data) {
        const newsResp = response?.data?.data;
        const updatedItem = {
          ...item,
          fullNews: newsResp?.results[0]?.news_details,
        };
        setSelectedNews(updatedItem);
        setIsDrawerOpen(true);
      }
    } catch (error) {
      console.error('Error fetching news detail:', error);
    }
  };

  const handleCompanyClick = (item) => {
    if (isPaytmMoney()) {
      const deeplink = newsWidgetListDeeplink.replace('{pml_id}', item.pml_id);
      openDeepLinkPaytmMoney(deeplink);
    } else {
      companyPageNavigation(item);
    }
  };

  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      setIsDrawerOpen(false);
      setSelectedNews(null);
    }
  };

  return (
    <div className={`${styles.allNewsDrawer} ${isOpen ? styles.isOpen : ''}`}>
      <div className={styles.drawerHeader}>
        <button
          type="button"
          className={styles.backButton}
          onClick={handleBackClick}
        >
          <img
            src={isDarkMode() ? BackArrowIconDark : BackArrowIconLight}
            alt="Back"
            // className={styles.backIcon}
          />
        </button>
        <div className={styles.title}>Stock News</div>
      </div>

      <div
        className={cx(styles.contentContainer, {
          [styles.contentContainerDarkMode]: isDarkMode(),
        })}
      >
        <div className={styles.tabContainer}>
          <span
            className={cx(styles.tabChip, {
              [styles.tabChipDark]: isDarkMode(),
              [styles.activeTab]:
                activeTab === NEWS_CATEGORY.HOT && !isDarkMode(),
              [styles.activeTabDark]:
                activeTab === NEWS_CATEGORY.HOT && isDarkMode(),
            })}
            onClick={() => handleTabClick(NEWS_CATEGORY.HOT)}
          >
            🔥 {NEWS_CATEGORY.HOT}
          </span>
          <span
            className={cx(styles.tabChip, {
              [styles.tabChipDark]: isDarkMode(),
              [styles.activeTab]:
                activeTab === NEWS_CATEGORY.CORPORATE && !isDarkMode(),
              [styles.activeTabDark]:
                activeTab === NEWS_CATEGORY.CORPORATE && isDarkMode(),
            })}
            onClick={() => handleTabClick(NEWS_CATEGORY.CORPORATE)}
          >
            {NEWS_CATEGORY.CORPORATE}
          </span>
        </div>

        <div className={styles.newsList} ref={newsListRef}>
          {isLoading ? (
            <Shimmer type="list" listProps={{ listSize: 5 }} />
          ) : localNewsItems.length > 0 ? (
            <>
              {localNewsItems.map((item) => (
                <div
                  key={getUniqueKey(item)}
                  className={styles.cardWrapper}
                  onClick={(e) => handleNewsCardClick(item, e)}
                >
                  <Card customClass={styles.newsCard}>
                    <div className={styles.newsCardHeader}>
                      <div
                        className={styles.companyInfo}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCompanyClick(item);
                        }}
                      >
                        <div className={styles.companyLogo}>
                          <CompanyIcon
                            name={item.pml_id}
                            className={styles.companyLogo}
                            type="stocks"
                            url={item.companyLogo}
                            fallbackImg={FallbackCompanyIcon}
                          />
                        </div>
                        <span className={styles.companyName}>
                          {item.companyName}
                        </span>
                      </div>

                      <StockChange
                        exchange={item.exchange}
                        segment={item.segment}
                        securityId={item.security_id}
                        instrumentType={item.instrument}
                        id={item.pml_id}
                        isBadge
                        stylesObj={{
                          positiveBadge: isDarkMode()
                            ? styles.positiveBadgeDark
                            : styles.positiveBadge,
                          negativeBadge: isDarkMode()
                            ? styles.negativeBadgeDark
                            : styles.negativeBadge,
                        }}
                        onBuySellClick={(e) => {
                          e.stopPropagation();
                          // todo:: Why we have this here?
                          // handleBuySellClick(item);
                        }}
                      />
                    </div>
                    <div className={styles.newsContent}>
                      <div className={styles.newsText}>{item.news}</div>
                      <div className={styles.timestamp}>{item.timestamp}</div>
                    </div>
                  </Card>
                </div>
              ))}
              <div ref={loadMoreRef} style={{ height: '20px' }} />
            </>
          ) : (
            <div className={styles.noNewsContainer}>
              <img
                src={noNewsIcon}
                alt="No news available"
                className={styles.noNewsIcon}
              />
              <h3 className={styles.noNewsTitle}>No Relevant News</h3>
              <div className={styles.noNewsSubtitle}>
                Meanwhile you can check other details, happy investing!
              </div>
            </div>
          )}
          {isFetchingMore && (
            <Shimmer type="list" listProps={{ listSize: 1 }} />
          )}
        </div>
      </div>
      {isDrawerOpen && selectedNews && (
        <div className={styles.drawerOverlay} onClick={handleBackgroundClick}>
          <NewsFeedDrawerWrapper
            isOpen={isDrawerOpen}
            isDrawerOpen={isDrawerOpen}
            onClose={() => {
              setIsDrawerOpen(false);
              setSelectedNews(null);
            }}
            selectedNews={selectedNews}
            onCompanyClick={handleCompanyClick}
            // onBuySellClick={handleBuySellClick}
            navigateTo={navigateTo}
            history={history}
          />
        </div>
      )}
    </div>
  );
};

export default AllNewsDrawer;
