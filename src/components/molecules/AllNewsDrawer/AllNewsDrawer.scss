@import '/src/commonStyles/variables.scss';
@import '/src/commonStyles/colors.scss';

.allNewsDrawer {
  position: fixed;
  top: 0;
  right: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--surface-level-1);
  transform: translateX(100%); /* Start off-screen to the right */
  transition: transform 0.3s ease-in-out; /* Add transition for smooth animation */
  z-index: 1000; /* Ensure it's above other content */
  overflow-y: hidden; /* Control scrolling on this level */
  display: flex;
  flex-direction: column;

  &.isOpen {
    transform: translateX(0); /* Slide into view */
  }

  .drawerHeader {
    display: flex;
    align-items: center;
    padding: 12px 16px;

    .backButton {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--icon-universal-strong);
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      border-radius: 20px;
      // border-width: 1px;
      // border: 1px solid var(--Border-grey200, rgba(16, 16, 16, 0.13));


      .backIcon {
        width: 20px;
        height: 20px;
        color: var(--icon-universal-inverse-strong);
      }
    }

    .title {
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: var(--text-neutral-strong);
      margin-left: 12px;
    }
  }

  .contentContainer {
    background-color: #E8E1E1;
    padding-top: 16px;
    padding-bottom: 16px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-top-width: 1px;
    height: 100vh;
  }
  .contentContainerDarkMode {
    background-color: #393434;
  }

  .tabContainer {
    display: flex;
    justify-content: flex-start; /* Align items to the start */
    padding: 12px 8px 12px 8px;
    padding-top: 0;
    border-bottom: none; /* Remove bottom border */
    // background-color: var(--surface-level-1);
    flex-shrink: 0; /* Prevent tabs from shrinking */
    gap: 8px; /* Add gap between chips */

    .tabChip {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 12px;
      border-radius: 8px; /* Rounded corners for chip shape */
      // border: 1px solid var(--Border-grey200, rgba(16, 16, 16, 0.13)); /* Border color */
      background-color: #FFFFFF;
      color: #101010;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;

    }
    .activeTab {
      background-color: #101010;
      color: #FFFFFF;
    }
    
    .tabChipDark {
      background-color: #101010;
      color: #EEEEEE;
    }
    .activeTabDark {
      background-color: #EEEEEE;
      color: #101010
    }
  }

  .newsList {
    flex-grow: 1; /* Allow news list to take remaining space */
    overflow-y: auto; /* Make news list content scrollable */
    padding: 0 8px 16px 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
    width: 100vw;
    box-sizing: border-box;

    .cardWrapper {
        cursor: pointer;
        width: 100%;
        -webkit-tap-highlight-color: rgba(0, 116, 255, 0.2);
    }

    .newsCard {
      padding: 16px;
    }

    .newsCardHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .companyInfo {
        display: flex;
        align-items: center;
        gap: 6px;
        max-width: 65%;

        .companyLogo {
          width: 18px;
          height: 18px;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .companyName {
          color: var(--text-neutral-strong);
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          min-width: 0;
        }
      }

      /* Badge styles are handled by the Badge component itself, but you might need to adjust spacing */

      // Add positive and negative badge styles from NewsFeedWidget.scss
      :global(.badge) {
        padding: 2px 6px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        height: 20px;
        display: flex;
        align-items: center;
        margin-left: 8px;
      }

      .positiveBadge {
        background: var(--BG-profitAndSuccessMedium, rgba(227, 246, 236, 1));
        color: var(--Text-profitAndSuccess, rgba(44, 176, 121, 1));
        height: 20px;
        padding: 2px 6px;
        font-size: 12px;
        line-height: 16px;
        font-weight: 600;
      }

      .negativeBadge {
        background: rgba(254, 226, 226, 0.95);
        color: var(--Text-lossAndError, rgba(235, 75, 75, 1));
        height: 20px;
        padding: 2px 6px;
        font-size: 12px;
        line-height: 16px;
        font-weight: 600;
      }

      .positiveBadgeDark {
        background: #0C3620;
        color: #02A85D;
        height: 20px;
        padding: 2px 6px;
      }
      
      .negativeBadgeDark {
        background: #40111B;
        color: rgba(235, 75, 75, 1);
        height: 20px;
        padding: 2px 6px;
      }
    }

    .newsContent {
      .newsText {
        font-size: 14px;
        color: var(--text-neutral-strong);
        margin-bottom: 6px;
      }

      .timestamp {
        font-size: 12px;
        color: var(--text-neutral-medium);
      }
    }

    .noNewsMessage {
      text-align: center;
      color: var(--text-neutral-medium);
      padding-top: 20px;
      font-size: 16px;
    }

    .noNewsContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%; /* Center content vertically in the container */
      text-align: center;
      padding: 3rem 1rem;
      background-color: #fff;
      text-align: center;
      border-radius: 0.75rem;
    }

    .noNewsIcon {
      display: block;
      width: 133px;
      object-fit: contain;
      z-index: 0;
    }

    .noNewsTitle {
      font-size: 16px;
      color: #101010;
      font-weight: 500;
      margin: 0;
      margin-top: 1rem;
      margin-bottom: 0.25rem;
      letter-spacing: 0.01px;
      line-height: 22px;
    }

    .noNewsSubtitle {
      font-size: 14px;
      color: var(--text-neutral-medium);
      margin: 0 auto;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.25rem;
      width: 300px;
    }
  }
}

.drawerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
} 