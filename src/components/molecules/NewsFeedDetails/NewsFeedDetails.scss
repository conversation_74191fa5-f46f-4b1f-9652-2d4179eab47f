@import '/src/commonStyles/variables.scss';

html, body {
  background-color: transparent !important;
}

.newsDetail {
  padding: 0;
  background-color: var(--background-pop-up); // Or match Figma background
  display: flex; // Enable flexbox layout
  flex-direction: column; // Stack children vertically
  height: 100%; // Ensure it takes full height within the drawer content
  overflow-y: hidden; // Prevent scrolling on this container, children will manage
  box-sizing: border-box;
  width: 100%;

  // Styles for the sticky header
  .drawerHeader {
    display: flex; // Use flexbox for internal layout of header content
    align-items: center;
    gap: 12px;
    padding: 16px 0; // Add desired padding around header content
    padding-top: 0;
    position: sticky; // Make the header sticky
    top: 0; // Stick to the top of the scrollable parent (.newsDetail)
    z-index: 10; // Ensure header is above scrolling content
    background-color: var(--background-pop-up); // Solid background for when it's sticky
    margin: 0; // Remove any default or previous margins
    box-sizing: border-box;
    flex-shrink: 0; // Prevent the header from shrinking

    .drawerHandle {
      width: 32px;
      height: 4px;
      background: var(--background-neutral-weak);
      border-radius: 2px;
      // margin: 8px auto;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 0;
    }

    .headerContent {
      margin-top: 0px; // Add space below the handle
      display: flex;
      align-items: center;
      padding: 0; // Padding is on the .drawerHeader parent
      gap: 12px;
      flex: 1; // Allow header content to take up available space
      min-width: 0; // Prevent content from overflowing

      .companyLogo {
        width: 36px;
        height: 36px;
        padding: 1px;
        background: var(--background-pop-up);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0; // Prevent logo from shrinking

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .titleSection {
        flex: 1; // Allow title section to take up space
        min-width: 0; // Prevent title section from overflowing

        .titleRow {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 2px;

          .title {
            color: var(--icon-neutral-strong);
            font-size: 16px;
            font-weight: 500;
            letter-spacing: -0.01em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80%;
          }

          .arrowIcon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--icon-neutral-strong);
            transform: rotate(180deg); // Rotate to point left
            flex-shrink: 0; // Prevent icon from shrinking
          }
        }

        .priceInfo {
          display: flex;
          align-items: baseline;

          .price {
            color: var(--icon-neutral-strong);
            font-size: 14px;
            font-weight: 400;
          }

          .change {
            font-size: 14px;
            font-weight: 400;

            &.positive {
              color: rgba(34, 197, 94, 0.95);
            }

            &.negative {
              color: rgba(239, 68, 68, 0.95);
            }
          }
        }
      }
    }
  }

  // Styles for the scrollable content body
  .newsDetailBody {
    flex-grow: 1; // Allows this section to take up all available vertical space
    min-height: 0; // Crucial for flex items with overflow to prevent overflow issues
    overflow-y: auto; // Make this section vertically scrollable
    display: flex; // Use flexbox for internal layout of content sections
    flex-direction: column; // Stack content sections vertically
    box-sizing: border-box;
    // padding: 0 16px; // Horizontal padding for the content area within the body
    gap: 16px; // Gap between sections/paragraphs within the body
    max-height: 60vh;
    // Styles for the newsDetailContent container inside newsDetailBody
    .newsDetailContent {
      // This container might not need significant layout styles if its children are directly styled
      // Based on the latest user changes, it wraps the header-like part and the full news paragraph
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      // No padding or margin here, applied to children or newsDetailBody
    }

    // Styles for the header-like section within newsDetailContent
    .newsDetailContentHeader {
      padding: 16px; // Padding inside this section
      border-radius: 16px; // Border radius from user changes
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 12px; // Gap between elements inside this header-like section
      // No margin here, applied to newsDetailBody or hotNewsCardlikeSection if used
    }

    .hotNewsContainer {
      padding: 12px;
      border-radius: 12px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .hotNewsContainerLight {
      background: linear-gradient(90deg, rgba(213, 232, 255, 0.5) 0%, rgba(211, 212, 255, 0.5) 100%); // Gradient from user changes
    }
    .hotNewsContainerDark {
      background: linear-gradient(90deg, var(--Vivid-gradientBlueLight, rgba(0, 17, 40, 0.50)) 0%, var(--Vivid-gradientPurpleLight, rgba(28, 17, 87, 0.50)) 100%);
    }

    // Styles for the Hot News Tag within newsDetailContentHeader
    .hotNewsTag {
      width: fit-content;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px 4px;

      span {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        background: linear-gradient(90deg, #FED533 0%, #EB4B4B 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .hotNewsTagBgLight {
      background: linear-gradient(90deg, var(--Vivid-yellowLight, rgba(255, 246, 178, 0.50)) 0%, var(--BG-lossAndErrorMedium, rgba(255, 235, 239, 0.50)) 100%);      
    }
    .hotNewsTagBgDark {
      background: linear-gradient(90deg, var(--Vivid-yellowLight, rgba(67, 52, 0, 0.50)) 0%, var(--BG-lossAndErrorMedium, rgba(64, 17, 27, 0.50)) 100%);
    }

    // Styles for the News Header within newsDetailContentHeader
    .newsHeader {
      display: flex;
      flex-direction: column;
      // gap: 4px;
      // No margin or padding here, applied to newsDetailContentHeader
    }

    // Typography styles for the News Title and Timestamp within newsHeader
    .newsTitle {
      color: var(--icon-neutral-strong);
      font-family: Inter;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0.01px;
      margin: 0;
    }

    .timestamp {
      color: var(--icon-neutral-strong);
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      margin-top: 8px;
    }

    // Styles for the full news paragraph within newsDetailContent
    .fullNews {
      margin-top: 24px;
      color: var(--icon-neutral-strong); // stone-950 opacity 95
      font-size: 14px; // sm
      font-weight: 400; // normal
      line-height: 20px; // leading-tight
      // No margin or padding here, applied to newsDetailContent or newsDetailBody
    }
  }

  // Styles for the sticky footer
  .actionButtons {
    padding: 16px 0; // Padding around the action buttons
    display: flex; // Use flexbox for buttons layout
    gap: 8px; // Gap between buttons
    background: var(--background-pop-up);
    position: sticky; // Make the footer sticky
    bottom: 0; // Stick to the bottom of the scrollable parent (.newsDetail)
    z-index: 1; // Ensure footer is above scrolling content, but below header
    box-sizing: border-box;
    flex-shrink: 0; // Prevent the footer from shrinking

    .button {
      flex: 1;
      // height: 40px;
      // border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: opacity 0.3s ease;
      // padding: 0 12px;
      width: 166px;
      height: 52px;
      gap: 4px;
      border-radius: 48px;
      padding-top: 15px;
      padding-right: 12px;
      padding-bottom: 15px;
      padding-left: 12px;


      &.buy {
        background: var(--BG-profitAndSuccess, rgba(44, 176, 121, 1));
        color: var(--Text-silverForever, rgba(255, 255, 255, 1));
        border: none;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;

        &:hover {
          opacity: 0.9;
        }
      }

      &.sell {
        background: var(--BG-lossAndError, rgba(235, 75, 75, 1));
        color: var(--Text-silverForever, rgba(255, 255, 255, 1));
        border: none;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }
} 