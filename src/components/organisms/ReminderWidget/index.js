/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState, useMemo, useEffect, useRef } from 'react';
import cx from 'classnames';
import {
  openDeepLinkPaytm,
  openDeepLinkPaytmMoney,
  exitApp,
  notifyNativeApp,
} from '@src/utils/bridgeUtils';
import { event_type, ORDER_PAD } from './enums';
import styles from './index.scss';
import {
  generateQueryParamsString,
  isDarkMode,
  log,
} from '../../../utils/commonUtil';
import { TRANSACTION_TYPES } from '../OrderPadLite/enum';
import { formatReminders, getCurrentDayName } from './utils';
import HeaderWrapper from '../../molecules/HeaderWrapper';
import { isPaytmMoney } from '../../../utils/coreUtil';
import {
  reminderWidgetListDeeplink,
  BUSINESS_TYPE_MAPPINGS,
} from '../../../utils/constants';
import { useReminderWidgetEvents } from './useReminderEvents';
import { PRODUCT_TYPES } from '../../../utils/Equities/enum';
import Carousel from '../../molecules/Carousel';
import OrderCard from './partials/OrderCard';
import FundsCard from './partials/FundsCard';
import Icon, { ICONS_NAME } from '../../molecules/Icon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../atoms/CompanyIcon/IconsList';
import GenericReminderCard from './partials/GenericReminderCard/index';

const ReminderWidget = (props) => {
  const {
    data: widgetData,
    companyPageNavigation: companyNavigationWidget,
    navigateTo,
    history,
    onOpen,
    isListPage = false,
    aggrKey,
    businessTypeFallback,
  } = props;
  log('widgetData: ', widgetData);

  const NUMBER_OF_CARDS_TO_SHOW = 5;

  const [currentReminders, setCurrentReminders] = useState([]);
  const [isFullPage, setIsFullPage] = useState(isListPage);
  const fullPageContainerRef = useRef(null);
  const [activeSlideIndex, setActiveSlideIndex] = useState(0);

  useEffect(() => {
    if (isFullPage) {
      // Lock scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore scroll
      document.body.style.overflow = '';
    }

    // Clean up just in case
    return () => {
      document.body.style.overflow = '';
    };
  }, [isFullPage]);

  const formattedData = useMemo(
    () => formatReminders(widgetData),
    [widgetData],
  );

  const {
    reminders,
    title,
    viewAll,
    businessType,
    subCohortId,
    buttons = {},
    closeButton,
  } = formattedData?.data || {};
  log('formattedData :: ', formattedData);

  useEffect(() => {
    if (reminders) {
      setCurrentReminders(reminders);
    }
  }, [reminders]);

  const analyticEvents = useReminderWidgetEvents({
    subCohortId,
  });

  const { isVisible } = closeButton || {};

  const handleRemoveReminder = (id) => {
    setCurrentReminders((prevReminders) => {
      const newReminders = prevReminders.filter((rem) => rem.id !== id);
      // If no cards are left after removal, notify native app to remove fragment
      if (newReminders.length === 0 && isPaytmMoney()) {
        const flowType =
          BUSINESS_TYPE_MAPPINGS[businessType]?.removeFragment ||
          'removeH5FragmentCombinedHome';
        log('Removing fragment with flowType:', flowType);
        notifyNativeApp({
          flowType,
          widgetId: widgetData?.widgetId,
        });
      }
      return newReminders;
    });
  };

  function navigateToBuySellScreen(isMainOrderpad, stock, txnType = 'buy') {
    const queryString = generateQueryParamsString({
      transactionType:
        txnType === 'buy' ? TRANSACTION_TYPES.BUY : TRANSACTION_TYPES.SELL,
      id: stock.id,
      securityId: stock.sec_id,
      exchange: stock.exchange,
      name: stock.name,
      segment: stock.segment,
      quantity: 1,
      instrumentType: stock.inst_type,
      isin: stock.isin,
      ...(stock?.product === PRODUCT_TYPES.MTF && {
        productType: stock?.product,
      }),
    });

    const route = isMainOrderpad ? ORDER_PAD : '/';

    navigateTo(history, `${route}${queryString}`, {}, 'push');
  }

  function navigateToSIP(stock) {
    const queryString = generateQueryParamsString({
      txnType: TRANSACTION_TYPES.BUY,
      sipOrderType: true,
      id: stock.id,
      securityId: stock.sec_id,
      exchange: stock.exchange,
      name: stock.name,
      segment: stock.segment,
      quantity: 1,
      instrumentType: stock.inst_type,
      isin: stock.isin,
      isSubsequentTrade: false,
    });

    navigateTo(history, `${ORDER_PAD}${queryString}`, {}, 'push');
  }

  const companyPageNavigation = (stock) => {
    if (companyNavigationWidget) {
      companyNavigationWidget(stock.id);
    } else {
      const instrumentType =
        stock.inst_type === 'ES' ? 'company' : stock.inst_type.toLowerCase();
      const { id } = stock;
      const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}`;
      openDeepLinkPaytmMoney(url);
    }
  };

  const onClickHandlerViaEventType = (button, stock) => {
    log('onClickHandlerViaEventType ::', { button, stock });
    analyticEvents.onCtaClick({
      label: stock.event_type,
      label2: stock.stock_name,
      label3: button.cta,
    });

    const instrumentType =
      stock.inst_type === 'ES' ? 'company' : stock.inst_type.toLowerCase();
    const { id, product } = stock;

    switch (stock.event_type) {
      case 'DOWNLOAD_APP':
        if (isPaytmMoney()) {
          openDeepLinkPaytmMoney(button.deeplink);
        } else {
          openDeepLinkPaytm(button.deeplinkMini);
        }
        break;

      case 'PENDING_LIMIT_ORDER':
        if (isPaytmMoney()) {
          const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}?action=place-order&txn_type=B&price=0&product=${product}&order_type=MKT`;
          openDeepLinkPaytmMoney(url);
        } else {
          navigateToBuySellScreen(true, stock, 'buy');
        }
        break;

      case 'SIP_WITHOUT_MANDATE':
        if (isPaytmMoney()) {
          const dayName = getCurrentDayName();
          const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}?action=place-order&txn_type=B&price=0&product=${product}&order_type=MKT&sip=true&frequency=weekly&day=${dayName}`;
          openDeepLinkPaytmMoney(url);
        } else {
          navigateToSIP(stock);
        }
        break;

      case 'FAILED_SELL_ORDER':
        if (isPaytmMoney()) {
          const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}?action=place-order&txn_type=S&price=0&product=${product}&order_type=MKT`;
          openDeepLinkPaytmMoney(url);
        } else {
          navigateToBuySellScreen(true, stock, 'sell');
        }
        break;

      default:
        break;
    }
  };

  const onClickHandler = (button, stock) => {
    log('onClickHandler ::', { button, stock });
    analyticEvents.onCtaClick({
      label: stock.event_type,
      label2: stock.stock_name,
      label3: button.cta,
    });
    if (isPaytmMoney()) {
      if (button.action === 'orderPad' || button.action === 'mainOrderPad') {
        const instrumentType =
          stock.inst_type === 'ES' ? 'company' : stock.inst_type.toLowerCase();
        const { id, product, exchange } = stock;
        const url = `https://paytmmoney.com/stocks/${instrumentType}/${id}?action=place-order&txn_type=B&price=0&product=${product}&order_type=MKT&exchange=${exchange}`;
        openDeepLinkPaytmMoney(url);
        return;
      }
      openDeepLinkPaytmMoney(button.deeplink);
    } else {
      switch (button.action) {
        case 'equitySIP':
          navigateToSIP(stock);
          break;
        case 'orderPad':
          navigateToBuySellScreen(false, stock);
          onOpen();
          break;
        case 'mainOrderPad':
          navigateToBuySellScreen(true, stock);
          break;
        case 'custom':
          openDeepLinkPaytm(button.deeplinkMini);
          break;
        default:
          break;
      }
    }
  };
  const getReminderDisplayConfig = (reminder) => {
    const eventType = reminder?.event_type;

    let showSubtitle = false;
    let showSymbol = false;
    let isNoLTP = false;

    switch (eventType) {
      case 'DOWNLOAD_APP':
        showSubtitle = true;
        break;

      case 'PENDING_LIMIT_ORDER':
      case 'FAILED_SELL_ORDER':
        showSymbol = true;
        break;

      case 'SIP_WITHOUT_MANDATE':
        isNoLTP = true;
        showSymbol = true;
        break;

      default:
        break;
    }

    return {
      showSubtitle,
      showSymbol,
      isNoLTP,
    };
  };

  const getReminderContent = (
    reminder,
    index,
    totalRemindersInView,
    currentSlideIndex,
  ) => {
    log('getReminderContent ::', reminder);
    const commonProps = {
      reminder,
      companyPageNavigation,
      buttons: buttons?.[reminder.event_type] || [],
      onClickHandler,
      showCloseIcon: isVisible,
      handleRemoveReminder,
      isFullPage,
      analyticEvents,
      key: reminder.id,
      index,
      totalRemindersInView,
      currentSlideIndex,
    };

    switch (reminder.event_type) {
      case event_type.order.ORDER_PAD_ABANDONEMENT:
      case event_type.order.STOCK_SIP_ABANDONEMENT:
      case event_type.order.FAILED_ORDERS:
        return <OrderCard {...commonProps} />;

      case event_type.funds.ADD_FUNDS_ABANDONEMENT:
      case event_type.funds.FAILED_ADD_FUNDS:
        return (
          <FundsCard
            {...commonProps}
            businessType={businessType}
            businessTypeFallback={businessTypeFallback}
          />
        );

      case event_type.other.DOWNLOAD_APP:
        return (
          <GenericReminderCard
            {...commonProps}
            getReminderDisplayConfig={getReminderDisplayConfig}
            onClickHandler={onClickHandlerViaEventType}
            onCardClickHandler={onClickHandlerViaEventType}
            logoType={COMPANY_ICONS_NAME.PML}
          />
        );

      case event_type.other.PENDING_LIMIT_ORDER:
      case event_type.other.SIP_WITHOUT_MANDATE:
      case event_type.other.FAILED_SELL_ORDER:
        return (
          <GenericReminderCard
            {...commonProps}
            getReminderDisplayConfig={getReminderDisplayConfig}
            onClickHandler={onClickHandlerViaEventType}
            onCardClickHandler={onClickHandlerViaEventType}
          />
        );

      default:
        return null;
    }
  };

  const handleViewAllClick = () => {
    analyticEvents.onViewAllClick();
    if (isPaytmMoney()) {
      const deeplink = `${reminderWidgetListDeeplink}&businessType=${businessType}&aggrKey=${aggrKey}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      setIsFullPage(true);
    }
  };

  const handleBackClick = () => {
    if (isPaytmMoney()) {
      exitApp();
    } else {
      setIsFullPage(false);
    }
  };

  useEffect(() => {
    if (isFullPage) {
      analyticEvents.onPageView({
        isFullPage,
      });
    } else if (currentReminders?.[0]) {
      analyticEvents.onPageView({
        isFullPage,
        label: currentReminders?.[0]?.event_type,
        label2:
          currentReminders[0]?.stock_name || currentReminders?.[0]?.default_amt,
        label3: currentReminders[0]?.default_amt || '',
      });
    }
  }, [analyticEvents, currentReminders, isFullPage]);

  const handleSlideScroll = () => {
    analyticEvents.onWidgetScroll({
      isFullPage,
    });
  };

  const handleSlideChange = (currentSlide) => {
    const currentReminder = currentReminders[currentSlide];
    log('currentReminder ::', currentReminder);

    if (currentSlide <= NUMBER_OF_CARDS_TO_SHOW - 1) {
      analyticEvents.onPageView({
        isFullPage,
        label: currentReminder.event_type,
        label2: currentReminder?.stock_name || currentReminder?.default_amt,
        label3: currentReminder?.default_amt || '',
      });
    }
  };

  useEffect(() => {
    if (!isFullPage) return;

    const element = fullPageContainerRef.current || window;
    let scrollTimeout;

    const handleScroll = () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        analyticEvents.onWidgetScroll({ isFullPage });
      }, 150);
    };

    element.addEventListener('scroll', handleScroll);

    return () => {
      element.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [analyticEvents, isFullPage]);

  if (currentReminders?.length === 0) return null;

  if (isFullPage) {
    return (
      <div className={styles.fullPageContainer}>
        <div className={styles.fullPageHeader}>
          <HeaderWrapper
            showBack={false}
            customTitleComponent={
              <div className={styles.headerTitle}>
                <div className={styles.backButton} onClick={handleBackClick}>
                  <Icon
                    name={
                      isDarkMode()
                        ? ICONS_NAME.BACK_ARROW_DARK
                        : ICONS_NAME.BACK_ARROW_LIGHT
                    }
                    width={18}
                    height={18}
                    iconStyles={styles.backButtonIcon}
                  />
                </div>
                <div>Reminders</div>
              </div>
            }
            onBackClick={handleBackClick}
          />
        </div>
        <div className={styles.mainContainer} ref={fullPageContainerRef}>
          {currentReminders?.map((reminder, index) =>
            getReminderContent(
              reminder,
              index,
              currentReminders.length,
              activeSlideIndex,
            ),
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cx(styles.mainContainer, isFullPage && styles.isFullPage)}>
      <div className={styles.header}>
        <div className={styles.text}>
          <div className={styles.title}>{title}</div>
        </div>
        {viewAll?.cta && !isFullPage && (
          <div className={styles.viewAllCard} onClick={handleViewAllClick}>
            <div>{viewAll?.cta}</div>
          </div>
        )}
      </div>
      {currentReminders?.length === 1 ? (
        getReminderContent(
          currentReminders[0],
          0,
          currentReminders.length,
          activeSlideIndex,
        )
      ) : (
        <Carousel
          customStyle={styles.bannersCarousel}
          customSetting={{
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: false,
            // autoplaySpeed: 1000,
            infinite: false,
            afterChange: (currentSlide) => {
              handleSlideChange(currentSlide);
              setActiveSlideIndex(currentSlide);
            },
            variableWidth: true,
            swipeToSlide: true,
            speed: 300,
            arrows: false,
            dots: false,
          }}
          onSwipeCallback={handleSlideScroll}
        >
          {currentReminders
            ?.slice(0, NUMBER_OF_CARDS_TO_SHOW)
            .map((reminder, index) =>
              getReminderContent(
                reminder,
                index,
                Math.min(NUMBER_OF_CARDS_TO_SHOW, currentReminders.length),
                activeSlideIndex,
              ),
            )}
        </Carousel>
      )}
    </div>
  );
};

export default React.memo(ReminderWidget);
