/* eslint-disable no-console */
import { useCallback, useEffect, useRef, useState } from 'react';
import cx from 'classnames';
import {
  getSupportedVersion,
  isDarkMode,
  isIosBuild,
} from '../../../../../utils/commonUtil';
import { BACKGROUND_IMAGES, event_type } from '../../enums';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import CompanyIcon from '../../../../atoms/CompanyIcon/CompanyIcon';
import AddFundsInput from '../../../../atoms/AddFundsInput/AddFundsInput';
import Button from '../../../../atoms/Button/Button';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../../../atoms/CompanyIcon/IconsList';
import {
  navigateToAddFunds,
  navigateToPaymentRedirect,
  navigateToTxnStatus,
} from '../../../../../utils/navigationUtil';
import useUpiIntentFlow from '../../../OrderPadLite/hooks/useUpiIntentFlow';
import useMakePaytmTpvPayment from '../../../OrderPadLite/hooks/useMakePaytmTpvPayment';
import { sanitizeAppName } from '../../../FirstFundCard/utils';
import {
  PAYMENT_MODES,
  PAYMENT_UPI_INTENT,
  QUICK_PAYMENT_DATA,
} from '../../../../../config/paymentConfig';
import {
  initiatePayment,
  useGetPaymentOptionsFms,
  usePaymentsConfig,
} from '../../../FirstFundCard/hooks/usePaymentQuery';
import { isEquityMiniApp } from '../../../../../utils/apiUtil';
import { cancelPayment } from '../../../../../actions/myOrderActions';
import styles from './index.scss';
import {
  getIntentAppName,
  useRepeatedUserConfig,
} from './hooks/useRepeatedUserConfig';
import { useGetQnR } from './hooks/useGetQnr';
import { isPaytmMoney } from '../../../../../utils/coreUtil';
import { BUSINESS_TYPE_MAPPINGS } from '../../../../../utils/constants';
import Shimmer from '../../../../atoms/Shimmer';
import { useCombinedIr } from '../../../../../query/readinessQuery';

const FundsCard = ({
  reminder,
  buttons,
  onClickHandler,
  showCloseIcon,
  handleRemoveReminder,
  isFullPage,
  analyticEvents,
  index,
  totalRemindersInView,
  businessType,
  businessTypeFallback,
}) => {
  console.log(
    'reminder :: businessType :: ',
    reminder,
    businessType,
    businessTypeFallback,
  );
  const { default_amt, event_type: eventType } = reminder;
  const [isLoading, setIsLoading] = useState(false);
  const [installedQnrIntentApp, setInstalledQnrIntentApp] = useState(null);
  const { upiIntentOptions } = useUpiIntentFlow();
  const { txnStatusdata, makePaytmTpvPayment } = useMakePaytmTpvPayment();
  const {
    data: paymentOptions,
    refetch: refetchPaymentOptions,
    isLoading: isPaymentOptionsLoading,
  } = useGetPaymentOptionsFms();
  console.log('paymentOptions ::', paymentOptions);
  const { data: paymentConfig } = usePaymentsConfig();
  const paymentMethods = paymentOptions?.paymentMethods || [];
  const qnrData = useGetQnR(paymentMethods);
  console.log('qnrData :: ', qnrData);

  const selectedBank = qnrData?.paymentMethodOptionList?.[0];
  console.log('selectedBank :: ', selectedBank);

  const { name, appLogo } = useRepeatedUserConfig({
    paymentMethodOption: selectedBank,
    isRepeatedUser: qnrData,
    selectedQNRPspApp: installedQnrIntentApp,
  });

  const { data } = usePaymentsConfig();
  const minkasuVersion = data?.payin?.minkasuVersion;
  const version = isIosBuild() ? minkasuVersion?.ios : minkasuVersion?.android;

  const { irData: urData, isLoading: isUrLoading } = useCombinedIr();

  const userReadinessData = useRef({
    hasInvested: null,
    irStatus: null,
  });

  if (!isUrLoading && data) {
    userReadinessData.current = {
      hasInvested: urData?.eqCashHasInvested,
      irStatus: urData?.eqCashIrStatus,
    };
  }

  const eventLabel3 = `${
    !isPaymentOptionsLoading
      ? selectedBank?.upiFlow === 'UPI_COLLECT'
        ? 'UPI Collect'
        : selectedBank?.upiFlow === 'UPI_INTENT'
          ? 'UPI Intent'
          : 'NetBanking'
      : selectedBank?.processName
        ? 'UPI Intent'
        : 'NetBanking'
  } | true`;

  const eventLabel4 = `${
    qnrData?.upiDetails?.intent?.name ||
    qnrData?.upiDetails?.lastUsedVpa?.toString()?.split('@')[1] ||
    selectedBank?.upiDetails?.collect?.lastUsedVpa?.toString()?.split('@')[1] ||
    selectedBank?.upiDetails?.intent?.name ||
    'false'
  } | ${
    qnrData?.isMultiBankEnabled
      ? 'false'
      : selectedBank?.ifscCode?.substring(0, 4) || null
  }`;

  const getNumericAmount = () =>
    default_amt && typeof default_amt === 'string'
      ? parseFloat(default_amt.split(',').join(''))
      : typeof default_amt === 'number'
        ? default_amt
        : 0;

  const onEditClick = () => {
    navigateToAddFunds({ amount: getNumericAmount() });
  };

  const callMakePaytmTpvPayment = useCallback(
    (tpvData) => makePaytmTpvPayment(tpvData),
    [makePaytmTpvPayment],
  );

  const getFindInstalledApp = (upiIntentOptionsT, appName) => {
    // For other PsP, name will be 'Paytm UPI' & for Paytm, name will be PayTM
    const otherPspApp = appName.includes('Paytm');
    if (otherPspApp) {
      return null;
    }
    return upiIntentOptionsT?.find(
      (apps) => apps?.appName?.toLowerCase() === appName?.toLowerCase(),
    );
  };

  useEffect(() => {
    if (qnrData) {
      console.log(
        'qnrData | upiIntentOptions :: ',
        qnrData,
        ' | ',
        upiIntentOptions,
      );
      setInstalledQnrIntentApp(
        getFindInstalledApp(
          upiIntentOptions,
          getIntentAppName(
            sanitizeAppName(
              qnrData?.paymentMethodOptionList[0]?.upiDetails?.intent?.name,
            ),
          ),
        ),
      );
    }
  }, [qnrData, upiIntentOptions]);

  const noAvailablePaymentMethod =
    !qnrData ||
    (qnrData?.paymentMethodOptionList?.[0]?.upiFlow ===
      QUICK_PAYMENT_DATA.UPI_INTENT &&
      !installedQnrIntentApp);
  console.log(
    'noAvailablePaymentMethod :: ',
    noAvailablePaymentMethod,
    !qnrData,
    !installedQnrIntentApp,
    qnrData,
  );

  const {
    header,
    description,
    pgImage,
    primaryCta,
    secondaryCta,
    exitIntentEnabled,
    android,
    flowType,
    ios,
  } = paymentConfig?.payin?.cancelAddMoney || {};
  const supportedVersion = getSupportedVersion(isIosBuild() ? ios : android);

  const onAddFundsClicked = async (button) => {
    setIsLoading(true);
    console.log(':: onAddFundsClicked ::');
    analyticEvents.onCtaClick({
      label: eventType,
      label2: reminder.default_amt,
      label3: button?.cta,
    });

    const amountValue = getNumericAmount();

    if (noAvailablePaymentMethod) {
      console.log(
        ':: navigateToAddFunds - !qnrData || !installedQnrIntentApp ::',
        qnrData,
      );
      sessionStorage.setItem('hasMovedToPaymentFLow', true);
      navigateToAddFunds({ amount: amountValue });
      return;
    }

    const makePaymentRequestBody = {
      amount: amountValue,
      id: selectedBank?.id,
      payments_txn_id: paymentOptions?.paymentsTxnId,
      payment_method: selectedBank?.paymentMethodId,
      vpa: selectedBank?.upiDetails?.collect?.lastUsedVpa,
      psp_app: installedQnrIntentApp?.processName
        ? installedQnrIntentApp?.appName
        : null,
      fms_txn_id: paymentOptions?.fmsTxnId,
      minkasupayRequired: getSupportedVersion(version, true),
    };

    sessionStorage.setItem(
      'fundsReminderTxnId',
      JSON.stringify({
        fmsTxnId: paymentOptions?.fmsTxnId,
        paymentsTxnId: paymentOptions?.paymentsTxnId,
      }),
    );

    console.log('initiatePayment ::: ', makePaymentRequestBody);
    const makePaymentRes = await initiatePayment(makePaymentRequestBody);
    console.log('makePaymentRes ::: ', makePaymentRes);

    if (makePaymentRes?.responseType) {
      const makePaytmTpvPaymentParams = {
        responseType: makePaymentRes.responseType,
        redirectionUrl: makePaymentRes.redirectionUrl,
        listenUrl: makePaymentRes.listenUrl,
        paymentsTxnId: makePaymentRes.paymentsTxnId,
        vpa: selectedBank?.upiDetails?.collect?.lastUsedVpa || null,
        processTxnRequestParams: makePaymentRes.processTxnRequestParams,
        amount: amountValue,
        upiDeepLink: makePaymentRes.upiDeepLink,
        processName: installedQnrIntentApp?.processName || null,
        source_payment: !isEquityMiniApp() ? 'pml' : '',
        mid: makePaymentRes?.processTxnRequestParams?.mid,
        txnToken: makePaymentRes?.processTxnRequestParams?.txnToken,
        fmsTxnId: paymentOptions?.fmsTxnId,
        flowType:
          BUSINESS_TYPE_MAPPINGS[businessType || businessTypeFallback]
            ?.refresh || 'refreshCombinedHome',
        widgetId: 'reminder-widget',
        exitIntentConfig: supportedVersion
          ? {
              exitIntentEnabled:
                exitIntentEnabled || getSupportedVersion(version),
              title: header,
              sub_title: description,
              image_url: pgImage,
              primary_cta: primaryCta,
              sec_cta: secondaryCta,
              flowType,
              eventDetails: {
                event_label: 'recurring',
                event_label3: eventLabel3,
                event_label4: eventLabel4,
                event_label6: `irstatus: ${userReadinessData.current.irStatus} | isinvestedstatus: ${userReadinessData.current.hasInvested}}'
                }`,
              },
            }
          : {},
      };
      console.log('makePaytmTpvPaymentParams ::: ', makePaytmTpvPaymentParams);
      if (isPaytmMoney()) {
        setIsLoading(false);
        navigateToPaymentRedirect({
          params: makePaytmTpvPaymentParams,
          isPml: isPaytmMoney(),
        });
      } else {
        console.log('::: callMakePaytmTpvPayment ::: ');
        setIsLoading(false);
        callMakePaytmTpvPayment(makePaytmTpvPaymentParams);
      }
    }
  };

  useEffect(() => {
    if (txnStatusdata) {
      const txnInfo = JSON.parse(
        sessionStorage.getItem('fundsReminderTxnId') || '{}',
      );

      if (txnStatusdata?.isTransactionDone) {
        console.log('payment successfull', txnStatusdata);
        navigateToTxnStatus({
          transactionId: txnInfo?.fmsTxnId,
          prevPage: 'combined-dashboard',
        });
        sessionStorage.removeItem('fundsReminderTxnId');
        sessionStorage.setItem('isPrevTransactionDone', true);
      } else {
        console.log('cancelling the payment');
        cancelPayment(txnInfo?.paymentsTxnId);
        sessionStorage.removeItem('fundsReminderTxnId');
        refetchPaymentOptions();
      }
    }
  }, [refetchPaymentOptions, txnStatusdata]);

  const isFailed = reminder?.event_type === event_type.funds.FAILED_ADD_FUNDS;

  const getCtaClassName = (btn) =>
    cx(styles.cta, {
      [styles.greenButton]: btn?.cta?.toLowerCase?.()?.includes('buy'),
    });

  const getTitle = () => {
    switch (reminder?.event_type) {
      case event_type.order.ORDER_PAD_ABANDONEMENT:
        return reminder?.title || 'Continue to add funds';
      case event_type.funds.FAILED_ADD_FUNDS:
        return reminder?.title || 'Your funds addition has failed';
      default:
        return reminder?.title || '';
    }
  };

  const getTitleClassName = () =>
    cx(styles.title, {
      [styles.rejectedText]: isFailed,
    });

  const getCardClassName = () =>
    cx(styles.card, {
      [styles.firstFullPageCard]: isFullPage && index === 0,
      [styles.partialCard]: !isFullPage,
      [styles.lastCard]: !isFullPage && index === totalRemindersInView - 1,
    });

  const getBackgroundImage = () => {
    const mode = isDarkMode() ? 'dark' : 'light';
    const imageConfig =
      BACKGROUND_IMAGES?.[reminder?.event_type] || BACKGROUND_IMAGES?.default;
    return `url(${imageConfig?.[mode] || ''})`;
  };

  const getIconType = () => {
    if (selectedBank?.paymentMethodId === PAYMENT_MODES.UPI) {
      console.log('UPI Logo');
      return COMPANY_ICONS_NAME.UPI;
    }
    if (selectedBank?.paymentMethodId === PAYMENT_MODES.NET_BANKING) {
      console.log('Net Banking Logo');
      return COMPANY_ICONS_NAME.FUNDS;
    }
    console.log('Default Logo');
    return COMPANY_ICONS_NAME.FUNDS;
  };

  return (
    <div
      style={{ backgroundImage: getBackgroundImage() }}
      className={getCardClassName()}
      key={reminder?.id}
      onClick={() => {
        analyticEvents.onWidgetClick({
          isFullPage,
          label: reminder.event_type,
          label2: reminder.default_amt,
        });
      }}
    >
      <div className={styles.headerWrapper}>
        <div className={styles.companyDetails}>
          <CompanyIcon
            name={reminder?.id}
            type={
              !noAvailablePaymentMethod &&
              selectedBank?.upiFlow === PAYMENT_UPI_INTENT.UPI_INTENT &&
              installedQnrIntentApp?.logoUrl
                ? ''
                : getIconType()
            }
            className={styles.companyIcon}
            url={
              !noAvailablePaymentMethod &&
              selectedBank?.upiFlow === PAYMENT_UPI_INTENT.UPI_INTENT &&
              installedQnrIntentApp?.logoUrl
                ? installedQnrIntentApp?.logoUrl
                : ''
            }
            isLoading={isPaymentOptionsLoading}
            iconVariant="funds"
          />

          <div className={styles.stockHeader}>
            <div className={getTitleClassName()}>{getTitle()}</div>
            <div className={styles.companyName}>
              {isPaymentOptionsLoading ? (
                <Shimmer type="line" height="20px" width="40%" />
              ) : noAvailablePaymentMethod ? (
                'Add funds to start investing!'
              ) : (
                name
              )}
            </div>
          </div>
          {showCloseIcon && (
            <Icon
              name={ICONS_NAME.CLOSE_ICON}
              className={styles.crossIcon}
              size={3.2}
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveReminder(reminder.id);
                analyticEvents.onDismiss({
                  label: eventType,
                  label2: reminder.default_amt,
                  label3: reminder.event_id,
                });
              }}
            />
          )}
        </div>
        <div className={styles.addFundsCtaContainer}>
          <div className={styles.addFundsWrapper}>
            <AddFundsInput
              value={getNumericAmount() || ''}
              name="amount"
              id="amount"
              data-testid="amount"
              inputMode="decimal"
              type="text"
              className={styles.addFundsInput}
              onEditClick={onEditClick}
            />
          </div>
          <div>
            {buttons?.map((button) => (
              <Button
                key={button?.cta}
                buttonText={button?.cta}
                className={getCtaClassName(button)}
                isLoading={isLoading}
                loadingText={button?.cta}
                onClickHandler={() =>
                  reminder?.event_type === 'ADD_FUNDS_ABANDONEMENT' ||
                  reminder?.event_type === 'FAILED_ADD_FUNDS'
                    ? onAddFundsClicked(button)
                    : onClickHandler?.(button, reminder)
                }
                buttonTextClassName={styles.buttonTextClassName}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundsCard;
