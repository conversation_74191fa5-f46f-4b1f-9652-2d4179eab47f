import cx from 'classnames';
import { useMemo } from 'react';
import { useGetMarginPerc } from '@src/query/stockQuery';
import CompanyIcon from '../../../../atoms/CompanyIcon/CompanyIcon';
import { ICONS_NAME as COMPANY_ICONS_NAME } from '../../../../atoms/CompanyIcon/IconsList';
import { isDarkMode, log } from '../../../../../utils/commonUtil';
import Button from '../../../../atoms/Button/Button';
import { BACKGROUND_IMAGES } from '../../enums';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import StockPriceChange from '../../../../molecules/StockPriceChange';
import { useStockAndInstrumentFeed } from '../../../../../utils/Equities/hooks';
import { PRODUCT_TYPES } from '../../../../../utils/Equities/enum';
import styles from '../FundsCard/index.scss';

const GenericReminderCard = ({
  reminder,
  buttons,
  onClickHandler,
  onCardClickHandler,
  showCloseIcon,
  handleRemoveReminder,
  isFullPage,
  analyticEvents,
  index,
  totalRemindersInView,
  getReminderDisplayConfig,
  customonClicked = () => {},
  logoType = COMPANY_ICONS_NAME.STOCKS,
}) => {
  log('$$$$ buttons ::', buttons);
  const { ltp, pClose } = useStockAndInstrumentFeed({
    exchange: reminder.exchange,
    segment: reminder.segment,
    securityId: reminder.sec_id,
    instrumentType: reminder.inst_type,
    id: reminder.pml_id,
  });

  const rangeValue = useMemo(() => {
    if (pClose && ltp) {
      return ((ltp - pClose) / Math.abs(pClose)) * 100;
    }
    return null;
  }, [ltp, pClose]);

  const { showSubtitle, showSymbol, isNoLTP } =
    getReminderDisplayConfig(reminder);
  log('getReminderDisplayConfig', { showSubtitle, showSymbol, isNoLTP });

  const getCtaClassName = (btn) => {
    log('getCtaClassName', btn);
    return cx(styles.cta, {
      [styles.greenButton]: btn?.cta?.toLowerCase?.()?.includes('download'),
      [styles.redButton]: btn?.cta?.toLowerCase?.()?.includes('sell'),
    });
  };

  const getTitleClassName = () => cx(styles.title, {});

  const getCardClassName = () =>
    cx(styles.card, {
      [styles.firstFullPageCard]: isFullPage && index === 0,
      [styles.partialCard]: !isFullPage,
      [styles.lastCard]: !isFullPage && index === totalRemindersInView - 1,
    });

  const getBackgroundImage = () => {
    const mode = isDarkMode() ? 'dark' : 'light';
    const imageConfig =
      BACKGROUND_IMAGES[reminder.event_type] || BACKGROUND_IMAGES.default;
    return `url(${imageConfig[mode]})`;
  };

  const { data } = useGetMarginPerc({
    query: {
      exchange: reminder.exchange,
      scrip_id: reminder.sec_id,
    },
    isEnabled: reminder.product === PRODUCT_TYPES.MTF,
  });

  return (
    <div
      className={getCardClassName()}
      style={{ backgroundImage: getBackgroundImage() }}
      key={reminder.id}
      onClick={() => {
        analyticEvents.onWidgetClick({
          isFullPage,
          label: reminder.event_type,
          label2: reminder.stock_name,
        });
        onCardClickHandler(buttons?.[0], reminder);
      }}
    >
      <div className={styles.headerWrapper}>
        <div className={styles.companyDetails}>
          <CompanyIcon
            name={reminder?.id}
            url={reminder?.logo}
            type={logoType}
            className={styles.companyIcon}
          />
          <div className={styles.stockHeader}>
            <div className={getTitleClassName()}>{reminder.title}</div>

            {showSubtitle ? (
              <span className={styles.subtitleText}>{reminder.subtitle}</span>
            ) : (
              <div className={styles.companyName}>
                {/* {reminder.product === PRODUCT_TYPES.MTF && (
                  <span className={styles.mtfChip}>MTF {data?.margin_x}x</span>
                )} */}
                {reminder.stock_name}
              </div>
            )}
          </div>
          {showCloseIcon ? (
            <Icon
              name={ICONS_NAME.CLOSE_ICON}
              className={styles.crossIcon}
              size={3.2}
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveReminder(reminder.id);
                analyticEvents.onDismiss({
                  label: reminder.event_type,
                  label2: reminder.stock_name,
                  label3: reminder.event_id,
                });
              }}
            />
          ) : null}
        </div>
        <div className={styles.returnsCtaContainer}>
          {showSubtitle ? (
            <div className={styles.returnsWrapper}>
              <span className={styles.descriptionText}>
                {reminder.description}
              </span>
            </div>
          ) : (
            <div className={styles.returnsWrapper}>
              <StockPriceChange
                fromComponent="reminder"
                securityId={reminder.sec_id}
                exchange={reminder.exchange}
                segment={reminder.segment}
                change={rangeValue}
                customClassName={styles.stockPriceChange}
                isSymbol={showSymbol}
                isnoLTP={isNoLTP}
              />
            </div>
          )}
          {buttons?.map((button) => (
            <Button
              key={button?.cta}
              buttonText={button?.cta}
              className={cx(getCtaClassName(button), styles.reminderCardButton)}
              onClickHandler={(e) => {
                e.stopPropagation();
                if (typeof onClickHandler === 'function') {
                  onClickHandler(button, reminder, e);
                } else {
                  customonClicked(button, reminder, e);
                }
              }}
              buttonTextClassName={styles.buttonTextClassName}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default GenericReminderCard;
