import { useCallback, useRef, useEffect } from 'react';
import { notifyNativeApp } from '../utils/bridgeUtils';
import { isIosBuild } from '../utils/commonUtil';

export const useNotifyHeight = (elementRef, widgetId, options = {}) => {
  const debounceTimerRef = useRef(null);
  const lastHeightRef = useRef(null);

  const notifyHeight = useCallback(() => {
    if (!elementRef.current) return;

    const { height = 0 } = elementRef.current.getBoundingClientRect();
    // Only notify if height actually changed
    if (height && height !== lastHeightRef.current) {
      lastHeightRef.current = height;
      console.log('element height:', height);
      notifyNativeApp({
        flowType: options?.flowType || 'combinedHomeH5FragmentHeight',
        height: isIosBuild()
          ? Math.ceil(height)
          : (Math.ceil(height) + 15).toString(),
        widgetId,
      });
    }
  }, [widgetId, elementRef]);

  const debouncedNotifyHeight = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(notifyHeight, options.delay || 300);
  }, [notifyHeight, options.delay]);

  useEffect(() => {
    if (!elementRef.current) return;

    const resizeObserver = new ResizeObserver(debouncedNotifyHeight);
    resizeObserver.observe(elementRef.current);
    // Initial height check
    debouncedNotifyHeight();

    return () => {
      clearTimeout(debounceTimerRef.current);
      resizeObserver.disconnect();
    };
  }, [debouncedNotifyHeight, elementRef]);

  return { notifyHeight, debouncedNotifyHeight };
};
