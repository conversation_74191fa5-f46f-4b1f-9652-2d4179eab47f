// import { getCookieValue } from './commonUtil';
// import { COOKIES } from './constants';

// const ssoToken = getCookieValue(COOKIES.SSO_TOKEN);
// const userAgent = JSON.parse(getCookieValue(COOKIES.USER_AGENT));
// const authorization = getCookieValue(COOKIES.AUTHORIZATION);
// const uid = getCookieValue(COOKIES.UID);

const bridgeData = {
  origin: 'paytm',
  device_type: 'android',
  client_type: 'android',
  isLogin: true,
  isIos: false,
  Authorization: '',
  sso_token: '', // replace with sso token
  userId: '', // replace with user id
  appVersionName: '2.1.2602',
  h5Version: '1.0.11-9.15.0-MB-H5-4468',
  loggedInType: 'deviceBinded',
  deviceName: 'motorola Moto G (5S)',
  H5NativeDeeplinkData: '',
  twoFAToken: '',
  deviceId: '928196fc4b360c1d',
};

export default bridgeData;
