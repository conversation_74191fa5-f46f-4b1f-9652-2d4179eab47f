import { Suspense, lazy } from 'react';
import GenericErrorPage from '../components/organisms/GenericErrorPage';
import CentralLoader from '../components/atoms/CentralLoader/CentralLoader';
import RootLoader from '../components/organisms/RootLoader';
import HomeLayout from '../components/templates/HomeLayout';
import ROUTES from './index';
import FirstStockLoader from '../components/atoms/FirstStockLoader/FirstStockLoader';
import ETFLoader from '../components/molecules/ETFLoader/ETFLoader';
import ReminderLoader from '../components/molecules/ReminderLoader/ReminderLoader';

const TradeCardPage = lazy(
  () => import(/* webpackChunkName: "TradeCard" */ './TradeCardPage'),
);

const FirstTradeProgressCard = lazy(
  () =>
    import(
      /* webpackChunkName: "FirstTradeProgressCard" */ './FirstTradeProgressCard'
    ),
);

const MfDailySipSelectAmount = lazy(
  () =>
    import(
      /* webpackChunkName: "MfDailySipSelectAmount" */ './MfDailySipSelectAmount'
    ),
);

const FundSelectionPopup = lazy(
  () =>
    import(
      /* webpackChunkName: "FundSelectionPopup" */ './FundSelectionPopupPage'
    ),
);

const FundSelectionPopupMonthly = lazy(
  () =>
    import(
      /* webpackChunkName: "FundSelectionPopupMonthly" */ './FundSelectionMonthlyPopupPage'
    ),
);

const OrderPadLite = lazy(
  () => import(/* webpackChunkName: "OrderPadLite" */ './OrderPadLiteDrawer'),
);

const SipCard = lazy(
  () => import(/* webpackChunkName: "SipCard" */ './SipCard/index'),
);

const GenericFundSection = lazy(
  () =>
    import(/* webpackChunkName: "GenericFundSection" */ './FundSection/index'),
);
const MonthlySipCard = lazy(
  () =>
    import(/* webpackChunkName: "MonthlySipCard" */ './MonthlySipCard/index'),
);

const TestDrawer = lazy(
  () => import(/* webpackChunkName: "TestDrawer" */ './testRoute'),
);

const FirstStockCard = lazy(
  () => import(/* webpackChunkName: "FirstStockCard" */ './FirstStockPage'),
);

const SelectWatchlist = lazy(
  () =>
    import(
      /* webpackChunkName: "SelectWatchlist" */
      /* webpackPreload: true */
      './SelectWatchlist'
    ),
);

const MFDailySIPDisclaimer = lazy(
  () =>
    import(
      /* webpackChunkName: "MFDailySIPDisclaimer" */ './MFDailySIPDisclaimer'
    ),
);

const ETFCard = lazy(
  () => import(/* webpackChunkName: "ETFCard" */ './ETFCard'),
);

const ETFThemedCard = lazy(
  () => import(/* webpackChunkName: "ETFThemedCard" */ './ETFThemedCard'),
);

const ETFSelectionPopup = lazy(
  () =>
    import(
      /* webpackChunkName: "ETFSelectionPopup" */
      /* webpackPreload: true */
      './ETFSelectionPopup'
    ),
);

const ETFThemedSelectionPopup = lazy(
  () =>
    import(
      /* webpackChunkName: "ETFThemedSelectionPopup" */
      /* webpackPreload: true */
      './ETFThemedSelectionPopup'
    ),
);

const WidgetLoader = lazy(
  () => import(/* webpackChunkName: "WidgetLoader" */ './WidgetLoader'),
);

const ReminderWidget = lazy(
  () => import(/* webpackChunkName: "ReminderWidget" */ './ReminderWidget'),
);

const ReminderWidgetList = lazy(
  () =>
    import(/* webpackChunkName: "ReminderWidgetList" */ './ReminderWidgetList'),
);

const NewsFeedWidget = lazy(
  () =>
    import(
      /* webpackChunkName: "NewsFeedWidget" */
      /* webpackPreload: true */
      './NewsFeedWidget'
    ),
);
const NewsDetailsDrawer = lazy(
  () =>
    import(
      /* webpackChunkName: "NewsDetailsDrawer" */
      /* webpackPreload: true */
      './NewsFeedList'
    ),
);

const AllNewsList = lazy(
  () =>
    import(
      /* webpackChunkName: "AllNewsList" */
      /* webpackPreload: true */
      './NewsListViewAll'
    ),
);

const FOIndexAnalysisWidget = lazy(
  () =>
    import(
      /* webpackChunkName: "FOIndexAnalysisWidget" */ './FOIndexAnalysisWidget'
    ),
);

const router = [
  {
    element: <RootLoader />,
    children: [
      {
        path: '/',
        element: <HomeLayout />,
        errorElement: <GenericErrorPage errorBoundary isWidget={false} />,
        children: [
          {
            path: `${ROUTES.GENERIC_FUND_SECTION}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <GenericFundSection />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.TRADE_CARD}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <TradeCardPage />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.FUNDSELECTION_POPUP}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <FundSelectionPopup />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.FUNDSELECTION_POPUP_MONTHLY}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <FundSelectionPopupMonthly />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.ORDER_PAD_LITE}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <OrderPadLite />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.SIP_CARD}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <SipCard />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.MONTHLY_SIP_CARD}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <MonthlySipCard />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.FIRST_STOCKS_CARD}`,
            element: (
              <Suspense fallback={<FirstStockLoader />}>
                <FirstStockCard />
              </Suspense>
            ),
          },
          {
            path: `/test`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <TestDrawer />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.MF_DAILY_SIP_SELECT_AMOUNT}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <MfDailySipSelectAmount />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.FIRST_TRADE_PROGRESS_CARD}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <FirstTradeProgressCard />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.MF_DAILY_SIP_DISCLAIMER}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <MFDailySIPDisclaimer />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.SELECT_WATCHLIST}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <SelectWatchlist />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.ETF_CARD}`,
            element: (
              <Suspense fallback={<ETFLoader />}>
                <ETFCard />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.ETF_THEMED_CARD}`,
            element: (
              <Suspense fallback={<ETFLoader />}>
                <ETFThemedCard />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.ETF_SELECTION_POPUP}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <ETFSelectionPopup />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.ETF_THEMED_SELECTION_POPUP}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <ETFThemedSelectionPopup />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.WIDGET_LOADER}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <WidgetLoader />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.REMINDER_WIDGET}`,
            element: (
              <Suspense fallback={<ReminderLoader />}>
                <ReminderWidget />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.REMINDER_WIDGET_LIST}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <ReminderWidgetList />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.NEWS_FEED_WIDGET}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <NewsFeedWidget />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.NEWS_FEED_WIDGET_DRAWER}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <NewsDetailsDrawer />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.ALL_NEWS_FEED_WIDGET_DRAWER}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <AllNewsList />
              </Suspense>
            ),
          },
          {
            path: `${ROUTES.FO_INDEX_ANALYSIS}`,
            element: (
              <Suspense fallback={<CentralLoader />}>
                <FOIndexAnalysisWidget />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: '*',
        element: <GenericErrorPage errorBoundary isWidget={false} />,
      },
    ],
  },
];

export default router;
