import { useEffect, useState } from 'react';
import queryString from 'query-string';

import FundSectionWrapper from '@src/ModuleEntry/FundSectionWrapper';
import FundSectionAggregatorComponent from '../../HOC/Aggregator/FundSectionAggregatorComponent';
import { AGGREGATOR_API } from '../../config/urlConfig';
import CentralLoader from '../../components/atoms/CentralLoader/CentralLoader';
import {
  notifyNativeApp,
  getStartupParamsAllCallback,
} from '../../utils/bridgeUtils';
import {
  isIosBuild,
  isDarkMode,
  setIsDailySipMF,
} from '../../utils/commonUtil';

import styles from './index.scss';

const FundSectionRouteWrapper = ({ pages = {} }) => (
  <FundSectionWrapper data={pages} />
);

const FundSectionRoute = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [nativeData, setNativeData] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { aggrKey, businessType } = query || {};

  const getWidgetHeight = () => {
    if (isIosBuild()) {
      return 300;
    }

    if (businessType === 'MUTUAL_FUND') {
      const { devicePixelRatio } = window;
      return (300 + 15) * devicePixelRatio;
    }

    return (300 + 15).toString();
  };

  const setFragmentHeight = () => {
    notifyNativeApp({
      flowType:
        businessType === 'MUTUAL_FUND'
          ? 'mfHomeH5FragmentHeight'
          : 'combinedHomeH5FragmentHeight',
      height: getWidgetHeight(),
      widgetId: 'generic-mf-widget',
    });
  };

  const addBodyStyles = () => {
    if (isIosBuild()) {
      document.body.classList.add(styles.nativeBodyMargin);
    } else if (businessType === 'MUTUAL_FUND' && isDarkMode()) {
      document.body.classList.add(styles.mfBGDark);
    } else if (businessType === 'MUTUAL_FUND') {
      document.body.classList.add(styles.mfBGLight);
    } else {
      document.body.classList.add(styles.nativeBodyMargin);
    }
  };

  const resizeMFViewPort = () => {
    const viewPortHeight = window.visualViewport.height;
    return Math.abs(315 - viewPortHeight) > 5;
  };

  useEffect(() => {
    if (aggrKey) {
      if (businessType === 'MUTUAL_FUND') {
        setIsDailySipMF(true);
      }

      if (isIosBuild() || resizeMFViewPort()) {
        setFragmentHeight();
      }

      addBodyStyles();
    }
  }, [aggrKey, businessType]);

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      if (result?.nativeData) {
        setNativeData(JSON.parse(result.nativeData));
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (!aggrKey) {
    return null;
  }

  if (isLoading) {
    return <CentralLoader />;
  }

  if (nativeData?.fundSectionData) {
    return (
      <FundSectionRouteWrapper pages={{ data: nativeData.fundSectionData }} />
    );
  }

  const WrappedComponent = FundSectionAggregatorComponent({
    queryProps: {
      name: 'FundSection',
      url: AGGREGATOR_API.MF_DASHBOARD_FUND,
      fallbackUrl: AGGREGATOR_API.MF_DASHBOARD_FUND,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <CentralLoader />,
  })(FundSectionRouteWrapper);

  return <WrappedComponent />;
};

export default FundSectionRoute;
