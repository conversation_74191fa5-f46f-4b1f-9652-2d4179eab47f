@mixin applyStyles($styles) {
  @each $key, $value in $styles {
    @if $value !=null {
      #{$key}: $value;
    }
  }
}

@mixin fontStyle(
  $family: null,
  $weight: null,
  $size: null,
  $lineheight: null,
  $letterSpacing: null
) {
  $styles: (
    font-family: $family,
    font-weight: $weight,
    font-size: $size,
    line-height: $lineheight,
    letter-spacing: $letterSpacing,
  );
  @include applyStyles($styles);
}

@mixin flex($justifyContent: null, $alignitems: null, $wrap: null, $gap: null) {
  display: flex;
  $styles: (
    justify-content: $justifyContent,
    align-items: $alignitems,
    flex-wrap: $wrap,
    gap: $gap,
  );
  @include applyStyles($styles);
}
