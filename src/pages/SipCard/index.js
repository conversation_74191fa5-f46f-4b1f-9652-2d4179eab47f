import React, { useEffect, useRef, useState } from 'react';
import cx from 'classnames';

import Shimmer from '../../components/atoms/Shimmer';
import Button from '../../components/atoms/Button/Button';
import Icon, { ICONS_NAME } from '../../components/molecules/Icon/index';
import FundSelectionPopup from '../../components/organisms/FundSelectionPopup/FundSelectionPopup';
import MfDailySipSelectAmount from '../MfDailySipSelectAmount';
import DailySipDisclaimer from '../../components/molecules/DailySipDisclaimer/DailySipDisclaimer';

import {
  useDailySIPAnalyticsEvents,
  useCustomAnalyticsEvents,
} from '../../hooks/useDailySIPAnalyticsEvents';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { useDrawer } from '../../components/molecules/Drawer/Drawer';
import { formatPrice } from '../../components/molecules/Prices';
import { convertToLakhsOrCrore, TEXT_DATA } from './utils';
import { isPaytmMoney, isSourceMFH5 } from '../../utils/coreUtil';
import {
  amountSelectionDeeplink,
  dailySipDisclaimerDeeplink,
  fundSelectionDeeplink,
} from '../../utils/constants';
import { openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import {
  getAbsoluteValue,
  isDarkMode,
  setDailySipCohort,
  getDailySipCohort,
  getIsDailySipMF,
} from '../../utils/commonUtil';
import { PULSE_STATICS } from './enums';

import dailyMfSipImage from '../../assets/images/daily-sip-invest-21.svg';

import styles from './StartYourDailySip.scss';

let timerId;
const THREE_YEARS = '3y';
// const THREE_YEARS = '1y';

const SipCard = ({ showAnimation, transitionTime, mf, showPopupHandler }) => {
  const loaderRef = useRef();
  const { absoluteReturns } = mf.fundReturns;
  const threeYearReturn = absoluteReturns.findIndex(
    (ar) => ar.name === THREE_YEARS,
  );
  const threeYearOrLessReturn =
    threeYearReturn !== -1
      ? absoluteReturns[threeYearReturn]
      : absoluteReturns[absoluteReturns.length - 1];
  const [internalShowAnimation, setInternalShowAnimation] = useState(false);

  useEffect(() => {
    setInternalShowAnimation(false);
    setTimeout(() => {
      setInternalShowAnimation(showAnimation);
    }, 0);
  }, [showAnimation, mf]);

  return (
    <div className={styles.cardRoot}>
      <div
        className={styles.sipTitle}
        onClick={() => {
          showPopupHandler(true);
        }}
      >
        <Icon url={mf.amcLogo} width={32} />
        <div className={styles.sipDetails}>
          <div className={styles.sipName}>{mf.schemeName}</div>
          <div className={styles.sipReturnsData}>
            <span className={styles.sipReturnsLabel}>
              {`${threeYearOrLessReturn.name.toUpperCase()}`} Abs. Returns:
            </span>
            <span
              className={`${styles.sipReturnsPercentage} ${threeYearOrLessReturn.percentage < 0 ? styles.negative : styles.positive}`}
            >
              {formatPrice(threeYearOrLessReturn.percentage, 2, false, false)}%
            </span>
          </div>
        </div>
        <div className={styles.dropDownIcon}>
          <Icon name={ICONS_NAME.BLACK_ARROW_DOWN} width="24px" />
        </div>
        {showAnimation && internalShowAnimation && (
          <div
            ref={loaderRef}
            className={cx(styles.sipLoader, {
              [styles.sipLoaderDark]: isDarkMode(),
            })}
            style={{ animationDuration: `${transitionTime - 0.1}s` }}
          />
        )}
      </div>

      {mf?.estimatedFutureValue ? (
        <span className={styles.sipWealthPlanText}>
          ₹{mf?.applicableSipAmount?.[0]} Daily to{' '}
          <p className={styles.finalWealthAmtText}>
            ₹
            {convertToLakhsOrCrore(
              mf?.estimatedFutureValue,
              mf?.applicableSipAmount?.[0],
            )}
          </p>{' '}
          * - Your 30 year Wealth Plan 🚀
        </span>
      ) : (
        <span className={styles.sipWealthPlanText}>
          {TEXT_DATA.RIGHT_TIME_TO_START_INVESTMENT}
        </span>
      )}
    </div>
  );
};

const StartYourDailySip = ({
  mfData: { popularMfs: data = [], transitionTime = 10 /* ...widgetConfig */ },
  userSelectedFund,
  showAmountSelectionPopup,
  showFundSelectionPopup,
  fragmentConfig,
}) => {
  const cancelInterval = useRef(null);
  const sipDataIndex = useRef(0);
  const [sipCardData, setSipCardData] = useState(
    userSelectedFund || data?.[sipDataIndex.current] || {},
  );
  const { source } = fragmentConfig;

  const { sendAnalyticsEventDailySIP } = useDailySIPAnalyticsEvents();

  const {
    isOpen: isDisclaimerOpen,
    onOpen: onDisclaimerOpen,
    onClose: onDisclaimerClose,
  } = useDrawer();

  const [showAnimation, setShowAnimation] = useState(
    !(transitionTime === 0 || data.length < 2) && !userSelectedFund,
  );

  const mfProjectionValue = sipCardData?.projectionBasedOn;

  useEffect(() => {
    clearInterval(cancelInterval.current);
    cancelInterval.current = null;
    if (userSelectedFund) {
      setShowAnimation(false);
      setSipCardData(userSelectedFund);
      clearInterval(cancelInterval.current);
    }
    if (!showAnimation) return;

    cancelInterval.current = setInterval(() => {
      sipDataIndex.current = (sipDataIndex.current + 1) % data.length;
      setSipCardData(userSelectedFund || data?.[sipDataIndex.current] || {});
    }, transitionTime * 1000);

    return () => {
      clearInterval(cancelInterval.current);
      cancelInterval.current = null;
    };
  });

  const handleShowFundSelectionPopup = () => {
    setShowAnimation(false);
    showFundSelectionPopup(
      userSelectedFund || data?.[sipDataIndex.current] || {},
    );
  };

  const onDisclaimerClick = () => {
    if (isPaytmMoney() && !isSourceMFH5()) {
      const deeplink = `${dailySipDisclaimerDeeplink}${getDailySipCohort() ? `&cohort=${getDailySipCohort()}` : ''}${getIsDailySipMF() ? '&isDailySipMF=true' : ''}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      onDisclaimerOpen();
    }

    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.ON_DISCLAIMER_CLICK,
    });
  };

  return (
    <div
      className={cx(styles.root, {
        [styles.darkModeRoot]: isDarkMode(),
      })}
    >
      <div className={styles.header}>
        {/* <Lottie
          style={{ position: 'absolute', top: '0', left: '0', width: '76px', height: '80px' }}
          className={styles.leftLottie}
          animationData={isDarkMode() ? bgLeftLottieDark : bgLeftLottie}
          autoplay
          loop
        /> */}
        <div className={styles.dailySipContainer}>
          <img src={dailyMfSipImage} alt={TEXT_DATA.DAILY_SIP_INVESTMENT} />
          {/* <div className={cx(styles.smallCapHeading, {
            [styles.smallCapHeadingDark]: isDarkMode()
          })}>{TEXT_DATA.SMALL_CAP_OPPORTUNITY_STARTS_AT_21}</div> */}
          <p className={styles.dailyMfSipSubText}>
            {TEXT_DATA.INVEST_SMALL_GROW_BIG}
          </p>
        </div>
        {/* <Lottie
          animationData={isDarkMode() ? headerLottieDark : headerLottie}
          autoplay
          loop={false}
        /> */}
        {/* <Lottie
          style={{ position: 'absolute', top: '0', right: '0', width: '76px', height: '80px' }}
          animationData={isDarkMode() ? bgRightLottieDark : bgRightLottie}
          autoplay
          loop
        /> */}
      </div>
      {sipCardData?.schemeName ? (
        <SipCard
          mf={sipCardData}
          showPopupHandler={handleShowFundSelectionPopup}
          showAnimation={showAnimation}
          transitionTime={transitionTime}
        />
      ) : (
        <Shimmer height="50px" width="100%" />
      )}

      {/* <Lottie
        animationData={
          isDarkMode()
            ? lowToHighPriceArrowLottieDark
            : lowToHighPriceArrowLottie
        }
        autoplay
        loop={false}
        height="124px"
        width="344px"
      /> */}

      <div className={styles.footer}>
        <Button
          isPrimary
          className={styles.startSipBtn}
          buttonText={TEXT_DATA.START_YOUR_DAILY_SIP_BTN}
          buttonTextClassName={styles.startSipBtnText}
          onClickHandler={() => showAmountSelectionPopup(sipCardData)}
        />

        <div className={styles.footerTextContainer}>
          {mfProjectionValue ? (
            <div className={styles.sipDisclaimerNote}>
              {TEXT_DATA.SIP_DISCLAIMER_NOTE(
                getAbsoluteValue(mfProjectionValue, 2),
              )}
            </div>
          ) : (
            <div />
          )}

          <Button
            buttonText={TEXT_DATA.READ_FULL_DISCLAIMER}
            className={styles.readDisclaimerBtn}
            buttonTextClassName={styles.readDisclaimerBtnText}
            onClickHandler={onDisclaimerClick}
          />
        </div>
      </div>
      <DailySipDisclaimer
        active={isDisclaimerOpen}
        triggerClose={onDisclaimerClose}
      />
    </div>
  );
};

const StartYourDailySipWrapper = ({
  data: { data, ...fragmentConfig },
  ...rest
}) => {
  const mfData = { ...data };
  const { source } = rest;
  const [userSelectedFund, setUserSelectedFund] = useState(null);
  const [preSelectedFund, setPreselectedFund] = useState(null);
  const sipCardRef = useRef(null);

  const { sendAnalyticsEventDailySIP } = useDailySIPAnalyticsEvents();
  const { sendCustomAnalyticsEvent } = useCustomAnalyticsEvents(
    getIsDailySipMF()
      ? PULSE_STATICS.VERTICAL_NAME
      : PULSE_STATICS.VERTICAL_NAME_HOME,
    getIsDailySipMF()
      ? PULSE_STATICS.SCREEN_NAME
      : PULSE_STATICS.SCREEN_NAME_HOME,
  );

  const {
    isOpen: showAmountSelectionPopup,
    onOpen: onAmountSelectionPopupOpen,
    onClose: onAmountSelectionPopupClose,
  } = useDrawer();

  const {
    isOpen: showFundSelectionPopup,
    onOpen: onFundSelectionPopupOpen,
    onClose: onFundSelectionPopupClose,
  } = useDrawer();

  const handleStorageChange = (event) => {
    if (event.key === 'selectedFund') {
      setUserSelectedFund(JSON.parse(event.newValue));
    }
  };

  useEffect(() => {
    if (mfData?.popularMfs) {
      const { subCohortId = '' } = mfData;
      setDailySipCohort(subCohortId);

      if (isPaytmMoney()) {
        sendCustomAnalyticsEvent({
          action: PULSE_STATICS.ACTION.DS_WIDGET,
          event: PULSE_STATICS.OPEN_SCREEN_EVENT,
        });
        window.addEventListener('storage', handleStorageChange);
      } else {
        sendAnalyticsEventDailySIP({
          action: PULSE_STATICS.ACTION.PAGE_LOAD,
          event: PULSE_STATICS.OPEN_SCREEN_EVENT,
        });
      }
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearTimeout(timerId);
    };
  }, [mfData, fragmentConfig]);

  const showFundSelectionPopupHandler = (fund) => {
    setPreselectedFund(fund);
    if (isPaytmMoney() && !isSourceMFH5()) {
      localStorage.setItem('preSelectedFund', JSON.stringify(fund));

      const deeplink = `${fundSelectionDeeplink}${getDailySipCohort() ? `&cohort=${getDailySipCohort()}` : ''}${getIsDailySipMF() ? '&isDailySipMF=true' : ''}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      onFundSelectionPopupOpen();
    }

    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.ON_FUND_DROPDOWN_CLICK,
    });
  };

  const showAmountSelectionPopupHandler = (fund) => {
    if (isPaytmMoney() && !isSourceMFH5()) {
      localStorage.setItem('selectedFund', JSON.stringify(fund));
      const deeplink = `${amountSelectionDeeplink}${getDailySipCohort() ? `&cohort=${getDailySipCohort()}` : ''}${getIsDailySipMF() ? '&isDailySipMF=true' : ''}`;
      openDeepLinkPaytmMoney(deeplink);
    } else {
      setUserSelectedFund(fund);
      onAmountSelectionPopupOpen();
    }

    sendAnalyticsEventDailySIP({
      action: PULSE_STATICS.ACTION.START_DAILY_SIP_CLICK,
    });
  };

  return (
    <div ref={sipCardRef} className={styles.sipCardContainer}>
      <StartYourDailySip
        mfData={mfData}
        userSelectedFund={userSelectedFund}
        fragmentConfig={{ ...fragmentConfig, ...rest }}
        showAmountSelectionPopup={showAmountSelectionPopupHandler}
        showFundSelectionPopup={showFundSelectionPopupHandler}
      />
      <FundSelectionPopup
        isOpen={showFundSelectionPopup}
        setFund={setUserSelectedFund}
        preSelectedFund={preSelectedFund}
        onClose={onFundSelectionPopupClose}
      />
      <MfDailySipSelectAmount
        fund={userSelectedFund}
        showAmountSelectionPopup={showAmountSelectionPopup}
        onAmountSelectionPopupClose={onAmountSelectionPopupClose}
      />
    </div>
  );
};

export default React.memo(withErrorBoundary(StartYourDailySipWrapper));
