@import '/src/commonStyles/variables.scss';

body {
  margin: 0;
}

.newsFeedWidget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // user-select: none;
  // background: var(--surface-level-1);
  position: relative;
  // z-index: 10;

  .newsFeedHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
    padding: 16px;
    padding-top: 0;
    padding-bottom: 12px;

    .headerLeft {
      display: flex;
      align-items: center;
      gap: 8px;

      .newsIcon {
        width: 20px;
        height: 20px;
        color: var(--icon-neutral-strong);
        flex-shrink: 0;
      }

      .headerText {
        position: relative;
        top: -20px;
        left: 0;

        .headlineCarousel {
          position: relative;
          height: 100%;

          .headline {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            color: var(--icon-neutral-strong);
            font-size: 14px;
            font-weight: 600;
            opacity: 0;
            transform: translateY(100%);
            transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
            white-space: nowrap;

            &.active {
              opacity: 1;
              transform: translateY(0);
            }
          }
        }
      }
    }

    .headerRight {
      .viewAll {
        color: #013DA6;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        white-space: nowrap;
      }
      .viewAllDarkMode {
        color: #0A86BF;
      }
    }
  }

  .overScrollContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 12px;
    overflow-x: scroll;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    user-select: none;
    padding: 0px 16px 0px 16px;
    
    &::-webkit-scrollbar {
      display: none;
    }
  
    .newsGroup {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .newsFeedContent {
        width: calc(100vw - 28px);
      
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 24px;
        font-weight: bold;
    
        .carouselContainer {
          display: flex;
          width: 100%;
    
          .carouselSlide {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
            padding-right: 0;
    
            .cardWrapper {
              cursor: pointer;
              -webkit-tap-highlight-color: rgba(0, 116, 255, 0.2);
              user-select: none;
              // margin: 0 16px;
              // width: calc(100vw - 32px);
              width: 100%;
            }
    
            .newsCard {
              padding: 16px;
              background: var(--surface-level-1);
              border-radius: 16px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              gap: 8px;
              transition: all 0.3s ease-in-out;
              cursor: pointer;
              transform: translateZ(0);
              height: fit-content;
              margin: 0;
    
              .newsCardHeader {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 24px;
    
                .companyInfo {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  min-width: 0;
                  flex: 1;
                  max-width: 80%;
    
                  .companyLogo {
                    padding: 1px;
                    // background: rgba(255, 255, 255, 0.95);
                    border-radius: 2px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    width: 18px;
                    height: 18px;
    
                    img {
                      width: 100%;
                      height: 100%;
                      object-fit: contain;
                    }
                  }
    
                  .companyName {
                    color: var(--text-neutral-strong);
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 20px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    min-width: 0;
                  }
                }
    
                :global(.badge) {
                  padding: 2px 6px;
                  border-radius: 6px;
                  font-size: 12px;
                  font-weight: 600;
                  line-height: 16px;
                  height: 20px;
                  display: flex;
                  align-items: center;
                  margin-left: 8px;
                }
    
                .positiveBadge {
                  background: var(--BG-profitAndSuccessMedium, rgba(227, 246, 236, 1));
                  color: var(--Text-profitAndSuccess, rgba(44, 176, 121, 1));
                  height: 20px;
                  padding: 2px 6px;
                }
    
                .negativeBadge {
                  background: rgba(254, 226, 226, 0.95);
                  color: var(--Text-lossAndError, rgba(235, 75, 75, 1));
                  height: 20px;
                  padding: 2px 6px;
                }
    
                .positiveBadgeDark {
                  background: #0C3620;
                  color: #02A85D;
                  height: 20px;
                  padding: 2px 6px;
                }
                
                .negativeBadgeDark {
                  background: #40111B;
                  color: rgba(235, 75, 75, 1);
                  height: 20px;
                  padding: 2px 6px;
                }
              }
    
              .newsContent {
                display: flex;
                flex-direction: column;
                gap: 6px;
    
                .newsText {
                  color: var(--text-neutral-medium);
                  font-size: 14px;
                  font-weight: 400;
                  line-height: 20px;
                  height: 40px;
                  display: -webkit-box;
                  line-clamp: 2;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                }
    
                .timestamp {
                  color: var(--text-neutral-weak);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                }
              }
            }
          }
        }
      }
    }
    
    // .testItem {
    //   min-width: 40vw;       // Ensure each item takes 40% of viewport width
    //   height: 100px;
    //   background-color: yellow;
    //   margin-right: 12px;    // Gap between items
    
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   font-size: 24px;
    //   font-weight: bold;
    // }
  }

  .paginationDots {
    display: inline-flex;
    align-self: center;
    justify-content: center;
    align-items: center;
    gap: 4px;
    margin: 0;
    margin-top: 8px;
    padding: 0;
    height: 6px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 9999px;
      background-color: var(--text-neutral-light);
      padding: 0;
      margin: 0;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s ease;
      flex-shrink: 0;

      &.active {
        background-color: var(--background-primary-strong)
      }

      &:hover:not(.active) {
        background-color: var(--text-neutral-light);
      }
    }
  }
}

.newsDrawer {
  :global(.drawer-content) {
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    padding: 0;
    max-height: 80vh;
    overflow-y: auto;
    // background: #F5F5F5;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  :global(.close-icon) {
    display: none !important;
  }

  .drawerHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    // padding: 16px;
    // border-bottom: 1px solid var(--border-neutral-medium);
    position: sticky;
    top: -20px;
    z-index: 10;
    background-color: var(--background-universal-strong);
    margin: 0;
    box-sizing: border-box;
    flex-shrink: 0;
    width: 100%;

    .drawerHandle {
      width: 32px;
      height: 4px;
      background: var(--background-neutral-weak);
      border-radius: 2px;
      // margin: 8px auto;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 0;
    }

    .headerContent {
      margin-top: 8px;
      display: flex;
      align-items: center;
      padding: 0;
      gap: 12px;
      flex: 1;
      min-width: 0;

      .companyLogo {
        width: 36px;
        height: 36px;
        padding: 1px;
        background: var(--background-universal-strong);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .titleSection {
        flex: 1;
        min-width: 0;

        .titleRow {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 2px;

          .title {
            color: var(--icon-neutral-strong);
            font-size: 16px;
            font-weight: 500;
            letter-spacing: -0.01em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .arrowIcon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--icon-neutral-strong);
            transform: rotate(180deg);
            flex-shrink: 0;
          }
        }

        .priceInfo {
          display: flex;
          align-items: baseline;
          gap: 4px;

          .price {
            color: var(--icon-neutral-strong);
            font-size: 14px;
            font-weight: 400;
          }

          .change {
            font-size: 14px;
            font-weight: 400;

            &.positive {
              color: rgba(34, 197, 94, 0.95);
            }

            &.negative {
              color: rgba(239, 68, 68, 0.95);
            }
          }
        }
      }
    }
  }

  .newsDetailBody {
    flex: 1;
    min-height: 0;
    overflow-y: visible;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 16px 0;
    max-height: 520px;
    overflow: auto;
    max-height: 464px;
  }

  .newsDetailContent {
    // padding: 0 16px;
    flex: 1;
    min-height: 0;
    overflow-y: visible;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    // margin: 0 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .newsDetailContentHeader {
      background: linear-gradient(90deg, rgba(213, 232, 255, 0.5) 0%, rgba(211, 212, 255, 0.5) 100%);
      padding: 16px;
      border-radius: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .hotNewsTag {
        width: 80px;
        height: 20px;
        gap: 10px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2px 4px;

        background: linear-gradient(90deg, rgba(255, 246, 178, 0.5) 0%, rgba(255, 235, 239, 0.5) 100%);
  
        span {
          color: rgba(250, 204, 21, 0.95);
          font-size: 12px;
          font-weight: 500;
          line-height: 16px;
        }
      }
  
      .newsHeader {
        display: flex;
        flex-direction: column;
        gap: 4px;
  
        .newsTitle {
          color: var(--icon-neutral-strong);
          font-size: 16px;
          font-weight: 500;
          line-height: 20px;
          letter-spacing: -0.01em;
        }
  
        .timestamp {
          color: rgba(28, 28, 28, 0.5);
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
        }
      }
    }


    .fullNews {
      color: var(--icon-neutral-strong);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .actionButtons {
    padding: 16px;
    display: flex;
    gap: 8px;
    background: var(--background-pop-up);
    position: sticky;
    bottom: -20px;
    z-index: 1;
    box-sizing: border-box;
    flex-shrink: 0;

    .button {
      flex: 1;
      // height: 40px;
      // border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: opacity 0.3s ease;
      // padding: 0 12px;
      width: 166px;
      height: 52px;
      gap: 4px;
      border-radius: 48px;
      padding-top: 15px;
      padding-right: 12px;
      padding-bottom: 15px;
      padding-left: 12px;


      &.buy {
        background: var(--BG-profitAndSuccess, rgba(44, 176, 121, 1));
        color: var(--Text-silverForever, rgba(255, 255, 255, 1));
        border: none;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;

        &:hover {
          opacity: 0.9;
        }
      }

      &.sell {
        background: var(--BG-lossAndError, rgba(235, 75, 75, 1));
        color: var(--Text-silverForever, rgba(255, 255, 255, 1));
        border: none;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
        &:hover {
          opacity: 0.9;
        }
      }
    }
  }
}