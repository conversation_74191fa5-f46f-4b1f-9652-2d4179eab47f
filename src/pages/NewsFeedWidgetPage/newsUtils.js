import { openDeepLinkPaytmMoney } from '../../utils/bridgeUtils';
import { generateQueryParamsString } from '../../utils/commonUtil';
import { isPaytmMoney, sendAnalyticsEventWidget } from '../../utils/coreUtil';
import { WIDGET_EVENTS } from './enums';

export const formattedTimestamp = (date, time) => {
  const dateTime = new Date(`${date}T${time}`);
  const options = {
    day: '2-digit',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };
  const formatted = dateTime.toLocaleString('en-US', options);

  // Adjust formatting to match "26 May, 11:53 AM"
  const [monthDay, timePart] = formatted.split(', ');
  const [month, day] = monthDay.split(' ');
  const output = `${day} ${month}, ${timePart}`;

  return output;
};

export const getFormattedNewsItem = (scrip) => ({
  id: scrip.security_id || scrip.isin,
  companyName: scrip.stock_name,
  isHotNews: scrip.section_name.indexOf('Hot') > -1,
  news: scrip.heading,
  timestamp: formattedTimestamp(scrip.date, scrip.time),
  ...scrip,
});

export const handleBuySellClick = (
  stock,
  transactionType,
  navigateTo,
  history,
) => {
  console.log('handleBuySellClick', {
    stock,
    transactionType,
    navigateTo,
    history,
  });
  const {
    id,
    pml_id,
    security_id,
    exchange,
    companyName: name,
    segment,
    isin,
    instrument,
    product,
  } = stock || {};
  const queryString = generateQueryParamsString({
    id,
    securityId: security_id,
    exchange,
    name,
    segment,
    transactionType,
    isin,
    siblings: JSON.stringify(stock.siblings || []),
    tickSize: stock.tick_size || 0,
    instrumentType: instrument,
    symbol: null,
    activeLocation: 'company-page',
    quantity: 0,
    lotSize: null,
  });

  sendAnalyticsEventWidget({
    ...WIDGET_EVENTS.BUY,
    label: id,
  });

  if (isPaytmMoney()) {
    const instrumentType =
      instrument === 'ES' ? 'company' : instrument.toLowerCase();
    const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}?action=place-order&txn_type=${transactionType}&price=0&product=${product}&order_type=MKT&exchange=${exchange}`;
    openDeepLinkPaytmMoney(url);
  } else {
    navigateTo(history, `/order-pad${queryString}`, {}, 'push');
  }
};
