/* eslint-disable import/no-extraneous-dependencies */
import cx from 'classnames';
import React, { useState, useCallback, useEffect, useRef } from 'react';
import queryString from 'query-string';
import { Card } from '@paytm-h5-common/paytm_common_ui';
import NewsIcon from '@assets/icons/news-Icon.svg';
// eslint-disable-next-line import/no-unresolved
import FallbackCompanyIcon from '@assets/icons/company_fallback_icon.svg';
import NewsFeedDrawerWrapper from '../NewsFeedList/index';
import AllNewsDrawer from '../../components/molecules/AllNewsDrawer/AllNewsDrawer';
import styles from './NewsFeedWidget.scss';
import { getFormattedNewsItem, handleBuySellClick } from './newsUtils';
import { getGenericAppHeaders, makeApiGetCall } from '../../utils/apiUtil';
import { AGGREGATOR_API, NEWS_WIDGET_API } from '../../config/urlConfig';
import StockChange from '../../components/organisms/StockChange/StockChange';
import { isPaytmMoney } from '../../utils/coreUtil';
import {
  newsWidgetListDeeplink,
  viewAllNewsListDeeplink,
  NEWS_WIDGET_ATTRIBUTES,
  BUSINESS_TYPE_MAPPINGS,
} from '../../utils/constants';
import {
  openDeepLinkPaytmMoney,
  getStartupParamsAllCallback,
} from '../../utils/bridgeUtils';
import CompanyIcon from '../../components/atoms/CompanyIcon/CompanyIcon';
import HeadlineCarousel from '../../components/molecules/HeadlineCarousel';
import NewsWidgetLoader from '../../components/molecules/NewsWidgetLoader';
import { useAnalyticsEventForWidget } from '../../hooks/analyticsHooks';
import { WIDGET_EVENTS } from './enums';
import { isDarkMode, log } from '../../utils/commonUtil';
import AggregatorComponent from '../../HOC/Aggregator/AggregatorComponent';
import { withErrorBoundary } from '../../HOC/WidgetErrorBoundary';
import { useNotifyHeight } from '../../hooks/useNotifyHeight';
import { sendErrorToBackend } from '../../actions/runtime';

const ANIMATION_DURATION = 300; // ms

function chunkArray(array, size) {
  const result = [];
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  return result;
}

export const NewsFeedWidget = (props) => {
  const {
    data = {},
    pages,
    businessType,
    companyPageNavigation,
    generateQueryParamsString,
    navigateTo,
    history,
  } = props;
  console.log('NewsFeedWidget :: props', props);
  const [, setCurrentIndex] = useState(0);
  const [selectedNews, setSelectedNews] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isAllNewsDrawerOpen, setIsAllNewsDrawerOpen] = useState(false);

  const [allNewsItems, setAllNewsItems] = useState([]);
  const { sendAnalyticsEventWidget } = useAnalyticsEventForWidget();

  const [aggrData, setAggrData] = useState(pages && pages.data ? pages : null);
  const newsWidgetCardRef = useRef(null);

  const [visibleGroupIndex, setVisibleGroupIndex] = useState(0);
  const groupRefs = useRef([]);

  useNotifyHeight(newsWidgetCardRef, data?.widgetId || aggrData?.widgetId, {
    flowType:
      BUSINESS_TYPE_MAPPINGS[businessType || pages?.data?.businessType]
        ?.height || 'stocksHomeH5FragmentHeight',
  });

  useEffect(() => {
    if (pages && pages.data) {
      setAggrData(pages);
    }
  }, [pages]);

  const attributes =
    aggrData?.data.widget.attributes ?? data?.data?.widget?.attributes;

  const newsItems = attributes
    ?.find((item) => item.name === NEWS_WIDGET_ATTRIBUTES.TOP_NEWS)
    ?.value?.map((item) => getFormattedNewsItem(item));
  const HEADLINES = attributes?.find(
    (item) => item.name === NEWS_WIDGET_ATTRIBUTES.TITLE,
  )?.value;

  const chunkedNewsItems = chunkArray(newsItems?.slice(0, 6) || [], 2);

  useEffect(() => {
    groupRefs.current = groupRefs.current.slice(0, chunkedNewsItems.length);
  }, [chunkedNewsItems.length]);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = Number(entry.target.getAttribute('data-group-index'));
            setVisibleGroupIndex(index);
          }
        });
      },
      {
        root: null,
        threshold: 0.5,
      },
    );

    groupRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      groupRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, [chunkedNewsItems.length]);

  const viewAllTitle = attributes?.find(
    (item) => item.name === NEWS_WIDGET_ATTRIBUTES.VIEW_ALL_TITLE,
  )?.value?.cta;
  const itemsPerPage = 2;
  const totalPages = Math.ceil(
    (newsItems?.slice(0, 6)?.length ?? 1) / itemsPerPage,
  );

  const handleSlideChange = useCallback(
    (newIndex) => {
      sendAnalyticsEventWidget(WIDGET_EVENTS.HORIZONTAL);
      if (isAnimating) return;
      setIsAnimating(true);
      setCurrentIndex(newIndex * 2);
      setTimeout(() => setIsAnimating(false), ANIMATION_DURATION);
    },
    [isAnimating],
  );

  const handleCardClick = useCallback(async (item, e, allNews) => {
    e.preventDefault();
    e.stopPropagation();
    if (allNews) {
      sendAnalyticsEventWidget({
        ...WIDGET_EVENTS.VIEW_ALL_CLICKED,
        label: item.pml_id,
      });
      return;
    }

    sendAnalyticsEventWidget({
      ...WIDGET_EVENTS.CLICK,
      label: item.pml_id,
    });
    const apiUrl = NEWS_WIDGET_API.DETAILS_API_URL;
    const queryParams = {
      isin: item.isin,
      sno: item.sno,
    };
    const headers = getGenericAppHeaders();
    try {
      const response = await makeApiGetCall({
        url: apiUrl,
        headers,
        queryParams,
      });

      if (response?.data?.data) {
        const newsResp = response?.data?.data;
        const updatedItem = {
          ...item,
          fullNews: newsResp?.results[0]?.news_details,
        };
        setSelectedNews(updatedItem);
        localStorage.setItem('CARD_NEWS', JSON.stringify(updatedItem));
      } else {
        console.error(
          'API response did not contain expected data structure.',
          response,
        );
        setSelectedNews(item);
        localStorage.setItem('CARD_NEWS', JSON.stringify(item));
      }

      if (isPaytmMoney()) {
        openDeepLinkPaytmMoney(newsWidgetListDeeplink);
      } else {
        setIsDrawerOpen(true);
      }
    } catch (error) {
      console.error('Error fetching news detail using makeApiGetCall:', error);
      setSelectedNews(item);
      if (isPaytmMoney()) {
        openDeepLinkPaytmMoney(newsWidgetListDeeplink);
      } else {
        setIsDrawerOpen(true);
      }
    }
  }, []);

  const handleViewAllClick = useCallback(
    async (param, from) => {
      const apiUrl = NEWS_WIDGET_API.CONSICE_API_URL;
      const section = param || 'HOT';
      const queryParams = {
        section,
        from,
      };

      const eventLabel =
        aggrData?.meta?.businessType || data?.data?.meta?.businessType;
      sendAnalyticsEventWidget({
        ...WIDGET_EVENTS.VIEW_ALL,
        label: eventLabel,
      });

      const headers = getGenericAppHeaders();
      try {
        const response = await makeApiGetCall({
          url: apiUrl,
          headers,
          queryParams,
        });
        if (response?.data?.data) {
          const newsResp = response?.data?.data;
          const formattedItems = newsResp?.results.map((item) =>
            getFormattedNewsItem(item),
          );
          const updatedNewsList =
            from > 0
              ? [...allNewsItems, ...formattedItems]
              : [...formattedItems];

          if (isPaytmMoney()) {
            localStorage.setItem(
              'ALL_NEWS',
              JSON.stringify({
                newsItems: updatedNewsList,
                section,
                from,
              }),
            );
            openDeepLinkPaytmMoney(viewAllNewsListDeeplink);
          } else {
            setIsAllNewsDrawerOpen(true);
            setAllNewsItems(Array.from(updatedNewsList));
          }
        }
      } catch (error) {
        console.error(
          'Error fetching news detail using makeApiGetCall:',
          error,
        );
      }
    },
    [allNewsItems],
  );

  if (!data && !aggrData) {
    try {
      log('sendErrorToBackend :: news-widget-no-data');
      sendErrorToBackend({
        level: 'error',
        key: 'news-widget-no-data',
        timestamp: new Date().toISOString(),
        data: JSON.stringify({}),
      });
      return null;
    } catch (err) {
      console.error('Error logging failed', err);
      return null;
    }
  }

  const getNewsContent = (item) => (
    <div key={item.id} className={styles.newsFeedContent}>
      <div className={styles.carouselContainer}>
        <div key={item.id} className={styles.carouselSlide}>
          <div
            key={item.id}
            className={styles.cardWrapper}
            onClick={(e) => handleCardClick(item, e)}
          >
            <Card customClass={styles.newsCard}>
              <div className={styles.newsCardHeader}>
                <div className={styles.companyInfo}>
                  <div className={styles.companyLogo}>
                    <CompanyIcon
                      name={item.pml_id}
                      className={styles.companyLogo}
                      type="stocks"
                      url={item.companyLogo}
                      fallbackImg={FallbackCompanyIcon}
                    />
                  </div>
                  <span className={styles.companyName}>{item.companyName}</span>
                </div>
                <StockChange
                  exchange={item.exchange}
                  segment={item.segment}
                  securityId={item.security_id}
                  instrumentType={item.instrument}
                  id={item.pml_id}
                  isBadge
                  stylesObj={{
                    positiveBadge: isDarkMode()
                      ? styles.positiveBadgeDark
                      : styles.positiveBadge,
                    negativeBadge: isDarkMode()
                      ? styles.negativeBadgeDark
                      : styles.negativeBadge,
                  }}
                />
              </div>
              <div className={styles.newsContent}>
                <div className={styles.newsText}>{item.news}</div>
                <div className={styles.timestamp}>{item.timestamp}</div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );

  const recordEventsAndCompanyPageNavigation = (pml_id) => {
    sendAnalyticsEventWidget({
      ...WIDGET_EVENTS.COMPANY_PAGE,
      label: pml_id,
    });
    companyPageNavigation(pml_id);
  };

  if (data || aggrData) {
    return (
      <div ref={newsWidgetCardRef} className={styles.newsFeedWidget}>
        <div className={styles.newsFeedHeader}>
          <HeadlineCarousel
            headlines={HEADLINES ?? ['News']}
            icon={NewsIcon}
            direction="horizontal"
          />
          <div className={styles.headerRight}>
            <span
              className={cx(styles.viewAll, {
                [styles.viewAllDarkMode]: isDarkMode(),
              })}
              onClick={() => handleViewAllClick('HOT', 0)}
              role="button"
              tabIndex={0}
            >
              {viewAllTitle}
            </span>
          </div>
        </div>

        <div className={styles.overScrollContainer}>
          {chunkedNewsItems.map((group, groupIndex) => (
            <div
              key={groupIndex}
              className={styles.newsGroup}
              ref={(el) => {
                groupRefs.current[groupIndex] = el;
              }}
              data-group-index={groupIndex}
            >
              {group.map((item, index) => getNewsContent(item, index))}
            </div>
          ))}
        </div>

        <div className={styles.paginationDots}>
          {[...Array(totalPages)].map((_, index) => (
            <button
              key={index}
              type="button"
              className={`${styles.dot} ${index === visibleGroupIndex ? styles.active : ''}`}
              onClick={() => handleSlideChange(index * itemsPerPage)}
              aria-label={`Go to page ${index + 1}`}
            />
          ))}
        </div>

        <NewsFeedDrawerWrapper
          isDrawerOpen={isDrawerOpen}
          onClose={() => {
            setIsDrawerOpen(false);
            setSelectedNews(null);
          }}
          selectedNews={selectedNews}
          handleBuySellClick={handleBuySellClick}
          companyPageNavigation={recordEventsAndCompanyPageNavigation}
          generateQueryParamsString={generateQueryParamsString}
          navigateTo={navigateTo}
          history={history}
        />

        <AllNewsDrawer
          newsItemsList={allNewsItems}
          onClose={() => {
            setIsAllNewsDrawerOpen(false);
            setAllNewsItems([]);
          }}
          onCardClick={handleCardClick}
          isOpen={isAllNewsDrawerOpen}
          setIsAllNewsDrawerOpen={setIsAllNewsDrawerOpen}
          companyPageNavigation={recordEventsAndCompanyPageNavigation}
          getAllConsiceNews={handleViewAllClick}
          navigateTo={navigateTo}
          history={history}
        />
      </div>
    );
  }
  return null;
};

const NewsFeedWidgetPage = () => {
  const [nativeData, setNativeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetId, setwidgetId] = useState(null);
  const query = queryString.parse(window.location?.search);
  const { businessType, aggrKey } = query || {};

  useEffect(() => {
    getStartupParamsAllCallback((result) => {
      log('check nativeData', result);
      if (result?.nativeData) {
        const parsedData = JSON.parse(result.nativeData);
        localStorage.setItem('reminderWidgetData', JSON.stringify(parsedData));
        setNativeData(parsedData);
        setwidgetId(result?.widgetId);
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return <NewsWidgetLoader />;
  }

  if (nativeData) {
    return (
      <NewsFeedWidget
        data={
          nativeData?.data
            ? nativeData
            : {
                widgetId: widgetId || nativeData?.widgetType,
                data: {
                  ...nativeData,
                },
              }
        }
        businessType={businessType}
        aggrKey={aggrKey}
      />
    );
  }

  const WrappedComponent = AggregatorComponent({
    queryProps: {
      name: 'NewsWidget',
      url:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD,
      fallbackUrl:
        BUSINESS_TYPE_MAPPINGS[businessType]?.aggrFallbackUrl ||
        AGGREGATOR_API.STOCKS_DASHBOARD_FALLBACK,
      queryParams: {
        keys: aggrKey,
      },
    },
    loader: <NewsWidgetLoader />,
  })(NewsFeedWidget);

  return <WrappedComponent aggrKey={aggrKey} />;
};

export const NewsFeedWidgetComponent = React.memo(NewsFeedWidget);

export default withErrorBoundary(NewsFeedWidgetPage, false);
