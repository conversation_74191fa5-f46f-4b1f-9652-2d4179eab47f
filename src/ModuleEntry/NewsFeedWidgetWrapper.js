import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import { NewsFeedWidgetComponent } from '../pages/NewsFeedWidgetPage';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';

const NewsFeedWidgetWrapper = (props) => {
  console.log('NewsFeedWidgetWrapper :: props', props);
  const BackPress = useNativeBackPress();

  const { bridgeData, dataFeed, ...rest } = props;
  if (bridgeData) {
    Object.entries(bridgeData).forEach(([key, val]) => {
      DeviceInfoProvider.setInfo(key, val);
    });
  }

  return (
    <QueryClientProvider client={ownQueryClient}>
      <AxiosProviderWrapper>
        <NativeDocumentHideContext>
          <AppContextProvider
            isFeedRequired={false} // Adjust if data feed is needed
            externalDataFeed={dataFeed}
          >
            <NativeBackPressContext.Provider value={BackPress}>
              <NewsFeedWidgetComponent {...rest} />
            </NativeBackPressContext.Provider>
          </AppContextProvider>
        </NativeDocumentHideContext>
      </AxiosProviderWrapper>
    </QueryClientProvider>
  );
};

export default withErrorBoundary(NewsFeedWidgetWrapper);
