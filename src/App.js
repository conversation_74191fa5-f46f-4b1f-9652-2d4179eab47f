/* eslint-disable no-restricted-exports */
import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import AppContextProvider from './contexts/AppContextProvider';
import OrdersBook from './contexts/OrdersBookContext';
import { ToastSnackbarProvider } from './contexts/ToastSnackbarProvider';
import AxiosProviderWrapper from './contexts/AxiosProviderWrapper';
import { queryClient as ownQueryClient } from './provider/ReactQueryProvider';
import routes from './routes/routes';

// import './App.scss';
// import CaptureLocationHOC from './HOC/CaptureLocationHOC';
import NativeDocumentHideContext from './contexts/NativeDocumentHideContext';
import useSignal from './utils/useSignal';
import { ThemeProvider } from './contexts/ThemeContext';

const getRoutes = () => createBrowserRouter(routes);

const App = () => {
  useSignal();
  return (
    <QueryClientProvider client={ownQueryClient}>
      <NativeDocumentHideContext>
        <AxiosProviderWrapper>
          <ThemeProvider>
            <AppContextProvider>
              {/* <CaptureLocationHOC> */}
              <OrdersBook>
              <ToastSnackbarProvider>
                <RouterProvider router={getRoutes()} />
              </ToastSnackbarProvider>
              </OrdersBook>
              {/* </CaptureLocationHOC> */}
            </AppContextProvider>
          </ThemeProvider>
        </AxiosProviderWrapper>
      </NativeDocumentHideContext>
    </QueryClientProvider>
  );
};

export default App;
