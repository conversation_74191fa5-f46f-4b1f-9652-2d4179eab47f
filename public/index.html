<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,user-scalable=no"
    />
    <meta
      httpEquiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Paytm Money</title>
    <link rel="preconnect" href="https://static.paytmmoney.com" crossorigin />
    <link
      rel="preconnect"
      href="<%= htmlWebpackPlugin.options.hostName %>"
      crossorigin
    />
  </head>

  <body>
    <script>
      var urlSearchParams = new URLSearchParams(window.location.search);
      var isDarkModeParam = urlSearchParams.get('darkmode');
      console.log("check isDarkModeParam", isDarkModeParam)
      var root = document.getElementsByTagName('html')[0];
      console.log("check root", root, document.getElementsByTagName('html'))
      console.log("check window.matchMedia", window.matchMedia('(prefers-color-scheme: dark)').matches)
      if (isDarkModeParam === 'true') {
        root.setAttribute('class', 'dark');
      }
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        root.setAttribute('class', 'dark');
      }

      // OS detection function
      const getMobileOperatingSystem = () => {
        try {
          const userAgent = navigator.userAgent || navigator.vendor || window.opera;
          if (/windows phone/i.test(userAgent)) {
            return 'windows';
          }
          if (/android/i.test(userAgent)) {
            return 'android';
          }
          if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            return 'ios';
          }
          return 'unknown';
        } catch (error) {
          console.error('Error in OS detection:', error);
          return 'unknown';
        }
      };

      const BUSINESS_TYPE_MAPPINGS = {
        STOCKS: {
          removeFragment: 'removeH5FragmentStocksHome',
        },
        MUTUAL_FUND: {
          removeFragment: 'removeH5FragmentMFHome',
        },
        FNO: {
          removeFragment: 'removeH5FragmentFNOHome',
        },
        ETF: {
          removeFragment: 'removeH5FragmentETFHome',
        },
        IPO: {
          removeFragment: 'removeH5FragmentIPOHome',
        },
        HOMESCREEN: {
          removeFragment: 'removeH5FragmentCombinedHome',
        },
      };

      // Delay logic for Android
      (function () {
        let timeoutId = null;
        try {
          const os = getMobileOperatingSystem();
          console.log('os ::', os);
          if (os === 'android') {
            timeoutId = setTimeout(() => {
              try {
                const rootElement = document.getElementById('root');
                if (rootElement && rootElement.children.length === 0) {
                  if (window.JSBridge && typeof JSBridge.call === 'function') {
                    JSBridge.call(
                      'getStartupParams',
                      { keys: [] },
                      (result) => {
                        try {
                          console.log('result ::', result);
                          const data = result?.nativeData || {};
                          let nativeData;
                          try {
                            nativeData = JSON.parse(data);
                          } catch (parseError) {
                            console.error('Error parsing nativeData:', parseError);
                            return;
                          }
                          console.log('nativeData ::', nativeData);
                          const flowType = BUSINESS_TYPE_MAPPINGS[nativeData?.meta?.businessType]?.removeFragment;
                          const widgetId = nativeData?.widget?.name;
                          
                          JSBridge.call(
                            'notifyNativeApp',
                            {
                              data: { flowType, widgetId },
                            },
                            (notifyResult) => {
                              try {
                                console.log('notifyNativeApp result ::', notifyResult);
                                // Clear timeout after successful notification
                                if (timeoutId) {
                                  clearTimeout(timeoutId);
                                  timeoutId = null;
                                }
                              } catch (notifyError) {
                                console.error('Error in notifyNativeApp callback:', notifyError);
                              }
                            }
                          );
                        } catch (callbackError) {
                          console.error('Error in getStartupParams callback:', callbackError);
                        }
                      }
                    );
                  }
                }
              } catch (timeoutError) {
                console.error('Error in setTimeout callback:', timeoutError);
              }
            }, 15000);

            // Cleanup timeout if component unmounts or page unloads
            window.addEventListener('beforeunload', () => {
              if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
              }
            });
          }
        } catch (error) {
          console.error('Error in Android delay logic:', error);
          // Clear timeout if there's an error
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
        }
      })();
    </script>

    <div id="root"></div>
  </body>
</html>