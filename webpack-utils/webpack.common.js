const path = require('path');
const webpack = require('webpack');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const { ModuleFederationPlugin } = require('webpack').container;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { dependencies } = require('../package.json');
const commonPaths = require('./common-paths');
const APP_VERSION = require('./version');

const isDebug = !process.argv.includes('release');
const removeConsoleLogs = process.argv.includes('noconsole');
const isDesktop = process.argv.includes('desktop');
const buildPath = isDesktop ? 'desktop' : 'mobile';
const isRelease = process.argv.includes('release');
const NODE_ENV = process.env.NODE_ENV || 'development';

const { 'react-router-dom': reactRouterDom, ...restDep } = dependencies;

const getCommonConfig = () => ({
  entry: {
    app: `${commonPaths.appEntry}/index.js`,
  },
  stats: { warnings: false },
  output: {
    uniqueName: 'pml-widgets',
    publicPath: 'auto',
    path: commonPaths.outputPath,
    filename: `${APP_VERSION}/${buildPath}/static/js/[name].[chunkhash:8].js`,
    chunkFilename: `${APP_VERSION}/${buildPath}/static/js/[name].[chunkhash:8].js`,
    assetModuleFilename: isDebug
      ? `images/[path][name].[contenthash:8][ext]`
      : `images/[path][contenthash:8][ext]`,
    crossOriginLoading: 'anonymous',
    globalObject: 'self',
  },
  module: {
    rules: [
      {
        test: /\.worker\.(js|ts)$/,
        use: [
          {
            loader: 'worker-loader',
            options: {
              filename: `${APP_VERSION}/${buildPath}/static/workers/[name].js`,
              publicPath: '/',
              inline: 'no-fallback',
            },
          },
          {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env'],
            },
          },
        ],
        type: 'javascript/auto',
      },
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: ['babel-loader'],
      },
      {
        test: /\.(js|jsx)$/,
        loader: 'string-replace-loader',
        options: {
          multiple: [
            {
              search: '__BUILD_PATH__',
              replace: buildPath,
            },
          ],
        },
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset/resource',
      },
      // { test: /\.css$/, use: ["style-loader", "css-loader"] },
      { test: /\.css$/, use: [MiniCssExtractPlugin.loader, 'css-loader'] },
      {
        test: /\.(scss|sass)$/,
        exclude: /node_modules/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              modules: {
                compileType: 'module',
                mode: 'local',
                localIdentName: isDebug
                  ? '[name]-[local]-[hash:base64:5]'
                  : '[hash:base64:5]',
              },
              sourceMap: isDebug,
              importLoaders: 1,
            },
          },
          'sass-loader',
          {
            loader: 'sass-resources-loader',
            options: {
              resources: commonPaths.commonCssStyles,
            },
          },
        ],
      },
      {
        test: /\.(woff(2)?|eot|ttf|otf|svg)$/,
        type: 'asset/resource',
      },
    ],
  },
  performance: {
    // hints: 'warning',
    maxAssetSize: 250000,
    maxEntrypointSize: 1000000,
  },
  optimization: {
    minimize: !isDebug,
    minimizer: isDebug
      ? []
      : [
          new TerserPlugin({
            terserOptions: {
              sourceMap: true,
              compress: {
                inline: false,
                drop_console: !!removeConsoleLogs,
              },
            },
          }),
          new CssMinimizerPlugin({
            minimizerOptions: {
              preset: [
                'default',
                {
                  discardComments: { removeAll: true },
                },
              ],
            },
          }),
        ],
    runtimeChunk: false,
    splitChunks: {
      chunks: 'all',
      maxSize: 500000,
      cacheGroups: {
        commons: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
      },
    },
    sideEffects: true,
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser.js',
    }),

    new Dotenv({
      path: `./.env.${NODE_ENV}`,
    }),
    new webpack.DefinePlugin({
      __BUILD__: `'${buildPath}'`,
      __ENV__: `'${NODE_ENV}'`,
      __isRelease__: isRelease,
    }),
    new ModuleFederationPlugin({
      name: 'PMLWIDGETS',
      library: {
        type: 'var',
        name: 'PMLWIDGETS',
      },
      filename: 'moduleEntryWidget02.js',
      exposes: {
        './MFSipCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/SipCardWrapper.js',
        ),
        './MFMonthlySipCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/MonthlySipCardWrapper.js',
        ),
        './FirstStockCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/FirstStockCardWrapper.js',
        ),
        './FirstTradeProgressCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/FirstTradeProgressCardWrapper.js',
        ),
        './LightweightCharts': path.resolve(
          __dirname,
          '../src/ModuleEntry/LightweightChartsWrapper.js',
        ),
        './GoldETFCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/GoldETFCardWrapper.js',
        ),
        './GoldETFThemedCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/GoldETFThemedCardWrapper.js',
        ),
        './ReminderWidget': path.resolve(
          __dirname,
          '../src/ModuleEntry/ReminderWidgetWrapper.js',
        ),
        './NewsFeedWidget': path.resolve(
          __dirname,
          '../src/ModuleEntry/NewsFeedWidgetWrapper.js',
        ),
        './FOIndexAnalysisWidget': path.resolve(
          __dirname,
          '../src/ModuleEntry/FOIndexAnalysisWidgetWrapper.js',
        ),
        './FundSectionCard': path.resolve(
          __dirname,
          '../src/ModuleEntry/FundSectionWrapper.js',
        ),
        './TestWrapper': path.resolve(
          __dirname,
          '../src/ModuleEntry/TestWrapper.js',
        ),
      },
      shared: {
        ...restDep,
        react: {
          singleton: true,
          requiredVersion: dependencies.react,
        },
        'react-dom': {
          singleton: true,
          requiredVersion: dependencies['react-dom'],
        },
        // 'react-router-dom': {
        //   singleton: true,
        //   requiredVersion: dependencies['react-router-dom'],
        // },
      },
    }),
  ],
  resolve: {
    alias: {
      '@src': path.resolve(__dirname, '../src'),
      '@assets': path.resolve(__dirname, '../src/assets'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@config': path.resolve(__dirname, '../src/config'),
      '@context': path.resolve(__dirname, '../src/context'),
      '@HOC': path.resolve(__dirname, '../src/HOC'),
      '@layout': path.resolve(__dirname, '../src/layout'),
      '@query': path.resolve(__dirname, '../src/query'),
      '@services': path.resolve(__dirname, '../src/services'),
      '@utils': path.resolve(__dirname, '../src/utils'),
    },
  },
});

module.exports = getCommonConfig;
